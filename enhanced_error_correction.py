#!/usr/bin/env python3
"""
增强的DNA数据存储纠错系统
实现Reed-Solomon纠错码、汉明码和多重冗余机制
"""

import numpy as np
from typing import List, Tuple, Dict, Optional
import hashlib
import random

class ReedSolomonEncoder:
    """Reed-Solomon纠错编码器（简化版）"""
    
    def __init__(self, n: int = 255, k: int = 223):
        """
        初始化RS编码器
        
        Args:
            n: 码字长度
            k: 信息位长度
            纠错能力: (n-k)/2
        """
        self.n = n  # 总长度
        self.k = k  # 数据长度
        self.t = (n - k) // 2  # 纠错能力
        
    def encode_block(self, data_block: bytes) -> bytes:
        """
        对数据块进行RS编码
        
        Args:
            data_block: 输入数据块（最大k字节）
            
        Returns:
            编码后的数据块（n字节）
        """
        # 简化实现：添加校验字节
        if len(data_block) > self.k:
            raise ValueError(f"数据块太大，最大{self.k}字节")
        
        # 填充到k字节
        padded_data = data_block + b'\x00' * (self.k - len(data_block))
        
        # 计算校验字节（简化版：使用多个CRC）
        check_bytes = b''
        for i in range(self.n - self.k):
            # 使用不同的多项式计算校验
            crc = self._calculate_crc(padded_data, i)
            check_bytes += crc.to_bytes(1, 'big')
        
        return padded_data + check_bytes
    
    def decode_block(self, encoded_block: bytes) -> Tuple[bytes, bool]:
        """
        解码RS编码的数据块
        
        Args:
            encoded_block: 编码的数据块
            
        Returns:
            (解码数据, 是否成功)
        """
        if len(encoded_block) != self.n:
            return b'', False
        
        data_part = encoded_block[:self.k]
        check_part = encoded_block[self.k:]
        
        # 验证校验字节
        errors = 0
        for i in range(len(check_part)):
            expected_crc = self._calculate_crc(data_part, i)
            actual_crc = check_part[i]
            if expected_crc != actual_crc:
                errors += 1
        
        # 简化的纠错：如果错误不多，尝试修复
        if errors <= self.t:
            if errors > 0:
                # 简单的错误修复策略
                data_part = self._attempt_correction(data_part, check_part)
            return data_part, True
        else:
            return data_part, False
    
    def _calculate_crc(self, data: bytes, polynomial_index: int) -> int:
        """计算CRC校验"""
        polynomials = [0x1D, 0x07, 0x0B, 0x15, 0x09, 0x1F, 0x11, 0x19]
        poly = polynomials[polynomial_index % len(polynomials)]
        
        crc = 0
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ poly
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc
    
    def _attempt_correction(self, data: bytes, check_bytes: bytes) -> bytes:
        """尝试纠错（简化版）"""
        # 简单策略：尝试翻转每一位，看是否能通过校验
        data_list = list(data)
        
        for byte_idx in range(len(data_list)):
            for bit_idx in range(8):
                # 翻转一位
                original_byte = data_list[byte_idx]
                data_list[byte_idx] ^= (1 << bit_idx)
                
                # 检查是否修复了错误
                test_data = bytes(data_list)
                errors = 0
                for i in range(len(check_bytes)):
                    expected_crc = self._calculate_crc(test_data, i)
                    if expected_crc != check_bytes[i]:
                        errors += 1
                
                if errors == 0:
                    return test_data
                
                # 恢复原值
                data_list[byte_idx] = original_byte
        
        return data


class HammingEncoder:
    """汉明码编码器"""
    
    @staticmethod
    def encode_byte(data_byte: int) -> int:
        """
        对单个字节进行汉明码编码
        
        Args:
            data_byte: 输入字节（8位）
            
        Returns:
            编码后的12位数据
        """
        # 将8位数据分布到12位中，留出校验位位置
        # 位置：1,2,4,8是校验位，其他是数据位
        bits = [(data_byte >> i) & 1 for i in range(8)]
        
        # 12位编码：p1,p2,d1,p4,d2,d3,d4,p8,d5,d6,d7,d8
        encoded = [0] * 12
        
        # 放置数据位
        data_positions = [2, 4, 5, 6, 8, 9, 10, 11]
        for i, pos in enumerate(data_positions):
            encoded[pos] = bits[i]
        
        # 计算校验位
        encoded[0] = encoded[2] ^ encoded[4] ^ encoded[6] ^ encoded[8] ^ encoded[10]  # p1
        encoded[1] = encoded[2] ^ encoded[5] ^ encoded[6] ^ encoded[9] ^ encoded[10]  # p2
        encoded[3] = encoded[4] ^ encoded[5] ^ encoded[6] ^ encoded[11]              # p4
        encoded[7] = encoded[8] ^ encoded[9] ^ encoded[10] ^ encoded[11]             # p8
        
        # 转换为整数
        result = 0
        for i, bit in enumerate(encoded):
            result |= (bit << i)
        
        return result
    
    @staticmethod
    def decode_byte(encoded_data: int) -> Tuple[int, bool]:
        """
        解码汉明码
        
        Args:
            encoded_data: 12位编码数据
            
        Returns:
            (解码字节, 是否成功)
        """
        # 提取12位
        bits = [(encoded_data >> i) & 1 for i in range(12)]
        
        # 计算校验子
        s1 = bits[0] ^ bits[2] ^ bits[4] ^ bits[6] ^ bits[8] ^ bits[10]
        s2 = bits[1] ^ bits[2] ^ bits[5] ^ bits[6] ^ bits[9] ^ bits[10]
        s4 = bits[3] ^ bits[4] ^ bits[5] ^ bits[6] ^ bits[11]
        s8 = bits[7] ^ bits[8] ^ bits[9] ^ bits[10] ^ bits[11]
        
        error_position = s1 + 2*s2 + 4*s4 + 8*s8
        
        # 如果有错误，尝试纠正
        if error_position != 0:
            if error_position < 12:
                bits[error_position] ^= 1  # 翻转错误位
            else:
                return 0, False  # 无法纠正的错误
        
        # 提取数据位
        data_positions = [2, 4, 5, 6, 8, 9, 10, 11]
        result = 0
        for i, pos in enumerate(data_positions):
            result |= (bits[pos] << i)
        
        return result, True


class TripleRedundancyEncoder:
    """三重冗余编码器"""
    
    @staticmethod
    def encode(data: bytes) -> bytes:
        """三重冗余编码"""
        return data + data + data
    
    @staticmethod
    def decode(encoded_data: bytes) -> Tuple[bytes, bool]:
        """三重冗余解码"""
        if len(encoded_data) % 3 != 0:
            return b'', False
        
        chunk_size = len(encoded_data) // 3
        chunk1 = encoded_data[:chunk_size]
        chunk2 = encoded_data[chunk_size:2*chunk_size]
        chunk3 = encoded_data[2*chunk_size:]
        
        # 多数表决
        result = bytearray()
        for i in range(chunk_size):
            votes = [chunk1[i], chunk2[i], chunk3[i]]
            # 选择出现次数最多的值
            majority = max(set(votes), key=votes.count)
            result.append(majority)
        
        # 检查是否有错误
        errors = sum(1 for i in range(chunk_size) 
                    if not (chunk1[i] == chunk2[i] == chunk3[i]))
        
        return bytes(result), errors <= chunk_size // 3  # 允许少量错误


class MultiLevelErrorCorrection:
    """多层纠错系统"""
    
    def __init__(self, 
                 use_reed_solomon: bool = True,
                 use_hamming: bool = True,
                 use_triple_redundancy: bool = False,
                 rs_redundancy: int = 32):
        """
        初始化多层纠错系统
        
        Args:
            use_reed_solomon: 是否使用RS码
            use_hamming: 是否使用汉明码
            use_triple_redundancy: 是否使用三重冗余
            rs_redundancy: RS码冗余字节数
        """
        self.use_reed_solomon = use_reed_solomon
        self.use_hamming = use_hamming
        self.use_triple_redundancy = use_triple_redundancy
        
        if use_reed_solomon:
            self.rs_encoder = ReedSolomonEncoder(n=255, k=255-rs_redundancy)
        
        self.hamming_encoder = HammingEncoder()
        self.triple_encoder = TripleRedundancyEncoder()
    
    def encode_data(self, data: bytes) -> Tuple[bytes, Dict]:
        """
        多层编码数据
        
        Args:
            data: 原始数据
            
        Returns:
            (编码数据, 编码信息)
        """
        encoded_data = data
        encoding_info = {
            'original_size': len(data),
            'layers': []
        }
        
        # 第一层：汉明码（如果启用）
        if self.use_hamming:
            hamming_encoded = bytearray()
            for byte in encoded_data:
                encoded_12bit = self.hamming_encoder.encode_byte(byte)
                # 将12位数据存储为2字节（浪费4位）
                hamming_encoded.extend(encoded_12bit.to_bytes(2, 'big'))
            
            encoded_data = bytes(hamming_encoded)
            encoding_info['layers'].append({
                'type': 'hamming',
                'size_before': len(data),
                'size_after': len(encoded_data)
            })
        
        # 第二层：Reed-Solomon码（如果启用）
        if self.use_reed_solomon:
            rs_encoded = bytearray()
            
            # 分块处理
            for i in range(0, len(encoded_data), self.rs_encoder.k):
                block = encoded_data[i:i+self.rs_encoder.k]
                if len(block) < self.rs_encoder.k:
                    block += b'\x00' * (self.rs_encoder.k - len(block))
                
                encoded_block = self.rs_encoder.encode_block(block)
                rs_encoded.extend(encoded_block)
            
            size_before = len(encoded_data)
            encoded_data = bytes(rs_encoded)
            encoding_info['layers'].append({
                'type': 'reed_solomon',
                'size_before': size_before,
                'size_after': len(encoded_data)
            })
        
        # 第三层：三重冗余（如果启用）
        if self.use_triple_redundancy:
            size_before = len(encoded_data)
            encoded_data = self.triple_encoder.encode(encoded_data)
            encoding_info['layers'].append({
                'type': 'triple_redundancy',
                'size_before': size_before,
                'size_after': len(encoded_data)
            })
        
        encoding_info['final_size'] = len(encoded_data)
        encoding_info['redundancy_ratio'] = len(encoded_data) / len(data)
        
        return encoded_data, encoding_info
    
    def decode_data(self, encoded_data: bytes, encoding_info: Dict) -> Tuple[bytes, Dict]:
        """
        多层解码数据
        
        Args:
            encoded_data: 编码数据
            encoding_info: 编码信息
            
        Returns:
            (解码数据, 解码结果)
        """
        decoded_data = encoded_data
        decode_results = {
            'layers': [],
            'total_errors_corrected': 0,
            'success': True
        }
        
        # 按相反顺序解码
        for layer_info in reversed(encoding_info['layers']):
            layer_type = layer_info['type']
            
            if layer_type == 'triple_redundancy':
                decoded_data, success = self.triple_encoder.decode(decoded_data)
                decode_results['layers'].append({
                    'type': 'triple_redundancy',
                    'success': success
                })
                if not success:
                    decode_results['success'] = False
            
            elif layer_type == 'reed_solomon':
                rs_decoded = bytearray()
                errors_corrected = 0
                
                # 分块解码
                for i in range(0, len(decoded_data), self.rs_encoder.n):
                    block = decoded_data[i:i+self.rs_encoder.n]
                    if len(block) == self.rs_encoder.n:
                        decoded_block, success = self.rs_encoder.decode_block(block)
                        if success:
                            rs_decoded.extend(decoded_block)
                        else:
                            rs_decoded.extend(block[:self.rs_encoder.k])
                            errors_corrected += 1
                
                decoded_data = bytes(rs_decoded)
                decode_results['layers'].append({
                    'type': 'reed_solomon',
                    'errors_corrected': errors_corrected,
                    'success': errors_corrected == 0
                })
                decode_results['total_errors_corrected'] += errors_corrected
            
            elif layer_type == 'hamming':
                hamming_decoded = bytearray()
                errors_corrected = 0
                
                # 每2字节解码为1字节
                for i in range(0, len(decoded_data), 2):
                    if i + 1 < len(decoded_data):
                        encoded_12bit = int.from_bytes(decoded_data[i:i+2], 'big')
                        decoded_byte, success = self.hamming_encoder.decode_byte(encoded_12bit)
                        hamming_decoded.append(decoded_byte)
                        if not success:
                            errors_corrected += 1
                
                decoded_data = bytes(hamming_decoded)
                decode_results['layers'].append({
                    'type': 'hamming',
                    'errors_corrected': errors_corrected,
                    'success': errors_corrected == 0
                })
                decode_results['total_errors_corrected'] += errors_corrected
        
        # 截取到原始长度
        original_size = encoding_info['original_size']
        decoded_data = decoded_data[:original_size]
        
        return decoded_data, decode_results


def demo_error_correction():
    """演示纠错系统"""
    print("=== 多层纠错系统演示 ===\n")
    
    # 创建纠错编码器
    error_corrector = MultiLevelErrorCorrection(
        use_reed_solomon=True,
        use_hamming=True,
        use_triple_redundancy=False,
        rs_redundancy=16
    )
    
    # 测试数据
    test_data = b"DNA Error Correction Test! 这是纠错测试数据。"
    print(f"原始数据: {test_data}")
    print(f"原始长度: {len(test_data)} 字节\n")
    
    # 编码
    encoded_data, encoding_info = error_corrector.encode_data(test_data)
    print(f"编码后长度: {len(encoded_data)} 字节")
    print(f"冗余比: {encoding_info['redundancy_ratio']:.2f}")
    print(f"编码层次: {[layer['type'] for layer in encoding_info['layers']]}\n")
    
    # 模拟传输错误
    corrupted_data = bytearray(encoded_data)
    error_positions = random.sample(range(len(corrupted_data)), min(5, len(corrupted_data)//10))
    
    print(f"模拟 {len(error_positions)} 个传输错误...")
    for pos in error_positions:
        corrupted_data[pos] ^= random.randint(1, 255)
    
    # 解码
    decoded_data, decode_results = error_corrector.decode_data(bytes(corrupted_data), encoding_info)
    
    print(f"解码成功: {decode_results['success']}")
    print(f"纠正错误数: {decode_results['total_errors_corrected']}")
    print(f"数据完整性: {decoded_data == test_data}")
    
    if decoded_data == test_data:
        print("✓ 所有错误已成功纠正！")
    else:
        print("✗ 仍有未纠正的错误")


if __name__ == "__main__":
    demo_error_correction()
