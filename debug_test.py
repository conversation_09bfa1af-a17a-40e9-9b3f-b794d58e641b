#!/usr/bin/env python3
"""
调试DNA编码
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder

def debug_encoding():
    """调试编码过程"""
    print("=== 调试编码过程 ===\n")
    
    # 创建编码器
    encoder = DNAEncoder(max_length=200, add_error_correction=False, use_fountain_code=False)
    
    # 测试简单数据
    test_data = b"AB"  # 简单的2字节数据
    
    print(f"原始数据: {test_data}")
    print(f"数据长度: {len(test_data)} 字节")
    print(f"十六进制: {test_data.hex()}")
    
    # 手动编码过程
    print(f"\n=== 编码过程 ===")
    
    # 1. 添加长度信息
    length_bytes = len(test_data).to_bytes(4, 'big')
    combined_data = length_bytes + test_data
    print(f"添加长度后: {combined_data.hex()}")
    
    # 2. 手动编码每个字节
    print("手动编码每个字节:")
    expected_dna = ""
    for byte_val in combined_data:
        high_nibble = (byte_val >> 4) & 0x0F
        low_nibble = byte_val & 0x0F

        high_pair = encoder.nibble_to_pair[high_nibble]
        low_pair = encoder.nibble_to_pair[low_nibble]

        byte_dna = high_pair + low_pair
        expected_dna += byte_dna

        print(f"  0x{byte_val:02X} -> {high_nibble:X}({high_pair}) + {low_nibble:X}({low_pair}) = {byte_dna}")

    print(f"期望DNA序列: {expected_dna}")

    # 使用编码器编码
    dna_sequence = encoder._encode_gc_balanced(combined_data)
    print(f"实际DNA序列: {dna_sequence}")
    print(f"DNA长度: {len(dna_sequence)} 碱基")
    print(f"编码匹配: {dna_sequence == expected_dna}")
    
    # 计算GC含量
    gc_count = dna_sequence.count('G') + dna_sequence.count('C')
    gc_percentage = (gc_count / len(dna_sequence)) * 100
    print(f"GC含量: {gc_percentage:.1f}%")
    
    # 3. 解码过程
    print(f"\n=== 解码过程 ===")

    # 手动解码每个4碱基组
    print("手动解码每个4碱基组:")
    for i in range(0, len(dna_sequence), 4):
        quartet = dna_sequence[i:i+4]
        if len(quartet) == 4:
            high_pair = quartet[:2]
            low_pair = quartet[2:]

            high_nibble = encoder.pair_to_nibble.get(high_pair, -1)
            low_nibble = encoder.pair_to_nibble.get(low_pair, -1)

            byte_val = (high_nibble << 4) | (low_nibble & 0x0F)

            print(f"  {quartet} -> {high_pair}({high_nibble}) + {low_pair}({low_nibble}) = 0x{byte_val:02X}")

    decoded_combined = encoder._decode_gc_balanced(dna_sequence)
    print(f"解码后数据: {decoded_combined.hex()}")

    # 提取长度
    if len(decoded_combined) >= 4:
        original_length = int.from_bytes(decoded_combined[:4], 'big')
        decoded_data = decoded_combined[4:]
        print(f"提取的长度: {original_length}")
        print(f"提取的数据: {decoded_data.hex()}")

        # 截取到原始长度
        final_data = decoded_data[:original_length]
        print(f"最终数据: {final_data.hex()}")
        print(f"解码成功: {final_data == test_data}")
    else:
        print("解码数据太短")

def test_nibble_mapping():
    """测试nibble映射"""
    print("\n=== 测试Nibble映射 ===\n")
    
    encoder = DNAEncoder()
    
    print("Nibble到DNA对映射:")
    for i in range(16):
        if i in encoder.nibble_to_pair:
            pair = encoder.nibble_to_pair[i]
            gc_count = pair.count('G') + pair.count('C')
            print(f"{i:2d} (0x{i:X}) -> {pair} (GC: {gc_count})")
    
    print("\nDNA对到Nibble映射:")
    for pair, nibble in sorted(encoder.pair_to_nibble.items()):
        gc_count = pair.count('G') + pair.count('C')
        print(f"{pair} (GC: {gc_count}) -> {nibble:2d} (0x{nibble:X})")

def main():
    """主函数"""
    test_nibble_mapping()
    debug_encoding()

if __name__ == "__main__":
    main()
