#!/usr/bin/env python3
"""
简单的DNA编码测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder

def test_simple_encoding():
    """测试简单编码"""
    print("=== 简单编码测试 ===\n")
    
    # 创建编码器（不使用错误校正，简化测试）
    encoder = DNAEncoder(max_length=200, add_error_correction=False, use_fountain_code=False)
    
    # 测试简单文本
    test_text = "Hello"
    test_data = test_text.encode('utf-8')
    
    print(f"原始文本: {test_text}")
    print(f"数据长度: {len(test_data)} 字节")
    print(f"数据内容: {test_data}")
    
    # 编码
    dna_sequences = encoder.encode_data(test_data)
    
    print(f"\nDNA序列数量: {len(dna_sequences)}")
    for i, seq in enumerate(dna_sequences):
        print(f"序列 {i+1}: {seq}")
        
        # 计算GC含量
        gc_count = seq.count('G') + seq.count('C')
        gc_percentage = (gc_count / len(seq)) * 100
        print(f"  长度: {len(seq)} 碱基")
        print(f"  GC含量: {gc_percentage:.1f}%")
        
        # 检查连续重复
        max_repeat = check_consecutive_repeats(seq)
        print(f"  最长连续重复: {max_repeat}")
    
    # 解码验证
    try:
        decoded_data = encoder.decode_data(dna_sequences)
        decoded_text = decoded_data.decode('utf-8')
        print(f"\n解码结果: {decoded_text}")
        print(f"编码解码成功: {decoded_text == test_text}")
    except Exception as e:
        print(f"解码失败: {e}")
        import traceback
        traceback.print_exc()

def test_chinese_encoding():
    """测试中文编码"""
    print("\n=== 中文编码测试 ===\n")
    
    # 创建编码器（不使用错误校正）
    encoder = DNAEncoder(max_length=200, add_error_correction=False, use_fountain_code=False)
    
    # 测试中文
    test_text = "你好"
    test_data = test_text.encode('utf-8')
    
    print(f"原始文本: {test_text}")
    print(f"数据长度: {len(test_data)} 字节")
    print(f"数据内容: {test_data}")
    
    # 编码
    dna_sequences = encoder.encode_data(test_data)
    
    print(f"\nDNA序列数量: {len(dna_sequences)}")
    total_gc = 0
    total_length = 0
    
    for i, seq in enumerate(dna_sequences):
        print(f"序列 {i+1}: {seq}")
        
        # 计算GC含量
        gc_count = seq.count('G') + seq.count('C')
        gc_percentage = (gc_count / len(seq)) * 100
        total_gc += gc_count
        total_length += len(seq)
        
        print(f"  长度: {len(seq)} 碱基")
        print(f"  GC含量: {gc_percentage:.1f}%")
        
        # 检查连续重复
        max_repeat = check_consecutive_repeats(seq)
        print(f"  最长连续重复: {max_repeat}")
    
    overall_gc = (total_gc / total_length) * 100 if total_length > 0 else 0
    print(f"\n总体GC含量: {overall_gc:.1f}%")
    
    # 解码验证
    try:
        decoded_data = encoder.decode_data(dna_sequences)
        decoded_text = decoded_data.decode('utf-8')
        print(f"解码结果: {decoded_text}")
        print(f"编码解码成功: {decoded_text == test_text}")
    except Exception as e:
        print(f"解码失败: {e}")
        import traceback
        traceback.print_exc()

def check_consecutive_repeats(sequence):
    """检查最长连续重复碱基数量"""
    if not sequence:
        return 0
    
    max_repeat = 1
    current_repeat = 1
    
    for i in range(1, len(sequence)):
        if sequence[i] == sequence[i-1]:
            current_repeat += 1
            max_repeat = max(max_repeat, current_repeat)
        else:
            current_repeat = 1
    
    return max_repeat

def main():
    """主函数"""
    test_simple_encoding()
    test_chinese_encoding()

if __name__ == "__main__":
    main()
