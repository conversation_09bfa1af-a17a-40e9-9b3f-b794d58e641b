#!/usr/bin/env python3
"""
测试nibble映射是否有问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder

def test_nibble_mapping():
    """测试nibble映射"""
    print("=== 测试Nibble映射 ===\n")
    
    encoder = DNAEncoder()
    
    print("Nibble到DNA对映射:")
    for i in range(16):
        if i in encoder.nibble_to_pair:
            pair = encoder.nibble_to_pair[i]
            gc_count = pair.count('G') + pair.count('C')
            print(f"{i:2d} (0x{i:X}) -> {pair} (GC: {gc_count})")
    
    print("\nDNA对到Nibble映射:")
    for pair, nibble in sorted(encoder.pair_to_nibble.items()):
        gc_count = pair.count('G') + pair.count('C')
        print(f"{pair} (GC: {gc_count}) -> {nibble:2d} (0x{nibble:X})")
    
    # 检查是否有重复映射
    print("\n=== 检查映射一致性 ===")
    
    # 检查nibble_to_pair的反向映射
    errors = 0
    for nibble, pair in encoder.nibble_to_pair.items():
        if pair not in encoder.pair_to_nibble:
            print(f"错误: 对 {pair} 不在反向映射中")
            errors += 1
        elif encoder.pair_to_nibble[pair] != nibble:
            print(f"错误: 对 {pair} 映射不一致: {nibble} != {encoder.pair_to_nibble[pair]}")
            errors += 1
    
    # 检查pair_to_nibble的反向映射
    for pair, nibble in encoder.pair_to_nibble.items():
        if nibble not in encoder.nibble_to_pair:
            print(f"错误: Nibble {nibble} 不在正向映射中")
            errors += 1
        elif encoder.nibble_to_pair[nibble] != pair:
            print(f"错误: Nibble {nibble} 映射不一致: {pair} != {encoder.nibble_to_pair[nibble]}")
            errors += 1
    
    if errors == 0:
        print("映射一致性检查通过")
    else:
        print(f"发现 {errors} 个映射错误")
    
    # 测试所有可能的字节值
    print("\n=== 测试所有字节值编码解码 ===")
    
    failed_bytes = []
    for byte_val in range(256):
        test_data = bytes([byte_val])
        
        # 编码
        dna_seq = encoder._encode_gc_balanced(test_data)
        
        # 解码
        decoded_data = encoder._decode_gc_balanced(dna_seq)
        
        if decoded_data != test_data:
            failed_bytes.append(byte_val)
            print(f"字节 0x{byte_val:02X} 编码解码失败:")
            print(f"  原始: {test_data.hex()}")
            print(f"  DNA: {dna_seq}")
            print(f"  解码: {decoded_data.hex()}")
    
    if failed_bytes:
        print(f"\n失败的字节值: {[hex(b) for b in failed_bytes]}")
    else:
        print("\n所有字节值编码解码测试通过")

def main():
    """主函数"""
    test_nibble_mapping()

if __name__ == "__main__":
    main()
