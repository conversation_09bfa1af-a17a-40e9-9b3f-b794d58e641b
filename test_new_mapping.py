#!/usr/bin/env python3
"""
测试新的nibble映射是否能减少重复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder

def test_new_mapping():
    """测试新的映射"""
    print("=== 测试新的Nibble映射 ===\n")
    
    encoder = DNAEncoder()
    
    print("新的Nibble到DNA对映射:")
    for i in range(16):
        if i in encoder.nibble_to_pair:
            pair = encoder.nibble_to_pair[i]
            gc_count = pair.count('G') + pair.count('C')
            repeat_risk = "高" if pair[0] == pair[1] else "低"
            print(f"{i:2d} (0x{i:X}) -> {pair} (GC: {gc_count}, 重复风险: {repeat_risk})")
    
    # 测试零字节序列
    print(f"\n=== 测试零字节序列 ===")
    zero_data = b'\x00' * 10  # 10个零字节
    
    dna_seq = encoder._encode_gc_balanced(zero_data)
    print(f"10个零字节编码为: {dna_seq}")
    print(f"DNA长度: {len(dna_seq)}")
    
    # 检查连续重复
    max_repeat = 0
    current_repeat = 1
    for i in range(1, len(dna_seq)):
        if dna_seq[i] == dna_seq[i-1]:
            current_repeat += 1
            max_repeat = max(max_repeat, current_repeat)
        else:
            current_repeat = 1
    
    print(f"最长连续重复: {max_repeat}")
    
    # 计算GC含量
    gc_count = dna_seq.count('G') + dna_seq.count('C')
    gc_percent = (gc_count / len(dna_seq)) * 100
    print(f"GC含量: {gc_percent:.1f}%")
    
    # 测试解码
    decoded = encoder._decode_gc_balanced(dna_seq)
    print(f"解码正确: {decoded == zero_data}")

def test_fountain_code_with_new_mapping():
    """测试喷泉码与新映射"""
    print(f"\n=== 测试喷泉码与新映射 ===")
    
    encoder = DNAEncoder(max_length=200, add_error_correction=False, 
                        use_fountain_code=True, fountain_redundancy=1.2)
    
    test_text = "人类是有极限的，我不做人啦"
    test_data = test_text.encode('utf-8')
    
    print(f"原始文本: {test_text}")
    print(f"数据长度: {len(test_data)} 字节")
    
    # 编码
    dna_sequences = encoder.encode_data(test_data)
    print(f"DNA序列数量: {len(dna_sequences)}")
    
    total_length = 0
    total_gc = 0
    max_repeat_overall = 0
    
    for i, seq in enumerate(dna_sequences):
        # 提取内容（去掉标识符）
        if seq.startswith("AAAA") and seq.endswith("TTTT"):
            content = seq[4:-4]
        else:
            content = seq
        
        # 检查连续重复
        max_repeat = 0
        current_repeat = 1
        for j in range(1, len(content)):
            if content[j] == content[j-1]:
                current_repeat += 1
                max_repeat = max(max_repeat, current_repeat)
            else:
                current_repeat = 1
        
        # 计算GC含量
        gc_count = content.count('G') + content.count('C')
        gc_percent = (gc_count / len(content)) * 100 if len(content) > 0 else 0
        
        print(f"\n序列 {i+1}:")
        print(f"  内容长度: {len(content)} 碱基")
        print(f"  GC含量: {gc_percent:.1f}%")
        print(f"  最长连续重复: {max_repeat}")
        
        total_length += len(content)
        total_gc += gc_count
        max_repeat_overall = max(max_repeat_overall, max_repeat)
    
    # 总体统计
    overall_gc_percent = (total_gc / total_length) * 100 if total_length > 0 else 0
    print(f"\n=== 总体统计 ===")
    print(f"总体GC含量: {overall_gc_percent:.1f}%")
    print(f"总长度: {total_length} 碱基")
    print(f"最长连续重复: {max_repeat_overall}")
    print(f"GC含量是否在理想范围(40-60%): {'是' if 40 <= overall_gc_percent <= 60 else '否'}")
    print(f"连续重复是否控制良好(<4): {'是' if max_repeat_overall < 4 else '否'}")
    
    # 测试解码
    try:
        decoded_data = encoder.decode_data(dna_sequences)
        decoded_text = decoded_data.decode('utf-8')
        print(f"\n=== 解码验证 ===")
        print(f"解码文本: {decoded_text}")
        print(f"编码解码成功: {decoded_text == test_text}")
    except Exception as e:
        print(f"\n解码失败: {e}")

def main():
    """主函数"""
    test_new_mapping()
    test_fountain_code_with_new_mapping()

if __name__ == "__main__":
    main()
