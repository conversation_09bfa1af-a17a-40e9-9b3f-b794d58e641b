#!/usr/bin/env python3
"""
DNA数据存储编码器 - 图形界面版本
使用PyQt5创建用户友好的界面
"""

import sys
import hashlib
from typing import List
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTextEdit, QPushButton, QLabel, 
                             QLineEdit, QCheckBox, QSpinBox, QTabWidget,
                             QGroupBox, QProgressBar, QMessageBox, QFileDialog,
                             QSplitter, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QTextCharFormat, QColor

class DNAEncoder:
    """DNA数据存储编码器类"""
    
    # 基本编码映射：2位二进制 -> 1个DNA碱基
    BINARY_TO_DNA = {
        '00': 'A',
        '01': 'T', 
        '10': 'G',
        '11': 'C'
    }
    
    # 解码映射：DNA碱基 -> 2位二进制
    DNA_TO_BINARY = {v: k for k, v in BINARY_TO_DNA.items()}
    
    def __init__(self, max_length: int = 200, add_error_correction: bool = True):
        self.max_length = max_length
        self.add_error_correction = add_error_correction
    
    def encode_data(self, data: bytes) -> List[str]:
        """将字节数据编码为DNA序列列表"""
        # 1. 转换为二进制字符串
        binary_str = ''.join(format(byte, '08b') for byte in data)
        
        # 2. 如果需要，添加填充使长度为2的倍数
        if len(binary_str) % 2 != 0:
            binary_str += '0'
        
        # 3. 添加数据长度信息
        original_length = len(data)
        length_binary = format(original_length, '032b')
        binary_str = length_binary + binary_str
        
        # 4. 如果启用错误校正，添加校验和
        if self.add_error_correction:
            checksum = hashlib.md5(data).digest()
            checksum_binary = ''.join(format(byte, '08b') for byte in checksum)
            binary_str = checksum_binary + binary_str
        
        # 5. 转换为DNA序列
        dna_sequence = self._binary_to_dna(binary_str)
        
        # 6. 分割为多个序列
        return self._split_sequence(dna_sequence)
    
    def decode_data(self, dna_sequences: List[str]) -> bytes:
        """将DNA序列列表解码为原始字节数据"""
        # 1. 合并所有DNA序列
        full_dna = ''.join(dna_sequences)
        
        # 2. 转换为二进制
        binary_str = self._dna_to_binary(full_dna)
        
        # 3. 提取校验和（如果有）
        if self.add_error_correction:
            checksum_binary = binary_str[:128]
            binary_str = binary_str[128:]
        
        # 4. 提取长度信息
        length_binary = binary_str[:32]
        original_length = int(length_binary, 2)
        binary_str = binary_str[32:]
        
        # 5. 转换为字节数据
        while len(binary_str) % 8 != 0:
            binary_str = binary_str[:-1]
        
        data = bytes(int(binary_str[i:i+8], 2) for i in range(0, len(binary_str), 8))
        data = data[:original_length]
        
        # 6. 验证校验和
        if self.add_error_correction:
            expected_checksum = hashlib.md5(data).digest()
            actual_checksum_binary = checksum_binary
            actual_checksum = bytes(int(actual_checksum_binary[i:i+8], 2) 
                                  for i in range(0, len(actual_checksum_binary), 8))
            
            if expected_checksum != actual_checksum:
                raise ValueError("数据校验失败，可能存在错误")
        
        return data
    
    def _binary_to_dna(self, binary_str: str) -> str:
        """将二进制字符串转换为DNA序列"""
        dna_sequence = ''
        for i in range(0, len(binary_str), 2):
            two_bits = binary_str[i:i+2]
            if len(two_bits) == 2:
                dna_sequence += self.BINARY_TO_DNA[two_bits]
        return dna_sequence
    
    def _dna_to_binary(self, dna_sequence: str) -> str:
        """将DNA序列转换为二进制字符串"""
        binary_str = ''
        for base in dna_sequence.upper():
            if base in self.DNA_TO_BINARY:
                binary_str += self.DNA_TO_BINARY[base]
        return binary_str
    
    def _split_sequence(self, dna_sequence: str) -> List[str]:
        """将长DNA序列分割为多个短序列"""
        sequences = []
        for i in range(0, len(dna_sequence), self.max_length):
            sequences.append(dna_sequence[i:i+self.max_length])
        return sequences
    
    def calculate_gc_content(self, dna_sequence: str) -> float:
        """计算GC含量"""
        gc_count = dna_sequence.count('G') + dna_sequence.count('C')
        return gc_count / len(dna_sequence) if len(dna_sequence) > 0 else 0
    
    def analyze_sequences(self, dna_sequences: List[str]) -> dict:
        """分析DNA序列的特性"""
        total_length = sum(len(seq) for seq in dna_sequences)
        gc_contents = [self.calculate_gc_content(seq) for seq in dna_sequences]
        
        return {
            'sequence_count': len(dna_sequences),
            'total_length': total_length,
            'average_length': total_length / len(dna_sequences) if dna_sequences else 0,
            'average_gc_content': sum(gc_contents) / len(gc_contents) if gc_contents else 0,
            'gc_content_range': (min(gc_contents), max(gc_contents)) if gc_contents else (0, 0)
        }

class EncodeThread(QThread):
    """编码线程"""
    finished = pyqtSignal(list, dict)
    error = pyqtSignal(str)
    
    def __init__(self, encoder, data):
        super().__init__()
        self.encoder = encoder
        self.data = data
    
    def run(self):
        try:
            sequences = self.encoder.encode_data(self.data)
            analysis = self.encoder.analyze_sequences(sequences)
            self.finished.emit(sequences, analysis)
        except Exception as e:
            self.error.emit(str(e))

class DecodeThread(QThread):
    """解码线程"""
    finished = pyqtSignal(bytes)
    error = pyqtSignal(str)
    
    def __init__(self, encoder, sequences):
        super().__init__()
        self.encoder = encoder
        self.sequences = sequences
    
    def run(self):
        try:
            data = self.encoder.decode_data(self.sequences)
            self.finished.emit(data)
        except Exception as e:
            self.error.emit(str(e))

class DNAEncoderGUI(QMainWindow):
    """DNA编码器图形界面"""
    
    def __init__(self):
        super().__init__()
        self.encoder = DNAEncoder()
        self.current_sequences = []
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("DNA数据存储编码器")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题
        title_label = QLabel("🧬 DNA数据存储编码器")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # 文本编码选项卡
        text_tab = self.create_text_tab()
        tab_widget.addTab(text_tab, "文本编码")
        
        # 文件编码选项卡
        file_tab = self.create_file_tab()
        tab_widget.addTab(file_tab, "文件编码")
        
        # 设置选项卡
        settings_tab = self.create_settings_tab()
        tab_widget.addTab(settings_tab, "设置")
    
    def create_text_tab(self):
        """创建文本编码选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 输入区域
        input_group = QGroupBox("输入文本")
        input_layout = QVBoxLayout(input_group)
        
        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText("请输入要编码的文本...")
        self.text_input.setMaximumHeight(100)
        input_layout.addWidget(self.text_input)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.encode_btn = QPushButton("🧬 编码为DNA")
        self.encode_btn.clicked.connect(self.encode_text)
        button_layout.addWidget(self.encode_btn)
        
        self.decode_btn = QPushButton("📝 解码为文本")
        self.decode_btn.clicked.connect(self.decode_dna)
        button_layout.addWidget(self.decode_btn)
        
        self.clear_btn = QPushButton("🗑️ 清空")
        self.clear_btn.clicked.connect(self.clear_all)
        button_layout.addWidget(self.clear_btn)
        
        input_layout.addLayout(button_layout)
        layout.addWidget(input_group)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # DNA序列输出区域
        dna_group = QGroupBox("DNA序列")
        dna_layout = QVBoxLayout(dna_group)
        
        self.dna_output = QTextEdit()
        self.dna_output.setReadOnly(True)
        self.dna_output.setFont(QFont("Courier", 10))
        dna_layout.addWidget(self.dna_output)
        
        splitter.addWidget(dna_group)
        
        # 分析结果区域
        analysis_group = QGroupBox("序列分析")
        analysis_layout = QVBoxLayout(analysis_group)
        
        self.analysis_output = QTextEdit()
        self.analysis_output.setReadOnly(True)
        self.analysis_output.setMaximumWidth(300)
        analysis_layout.addWidget(self.analysis_output)
        
        splitter.addWidget(analysis_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return widget

    def create_file_tab(self):
        """创建文件编码选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QHBoxLayout(file_group)

        self.file_path = QLineEdit()
        self.file_path.setReadOnly(True)
        self.file_path.setPlaceholderText("请选择要编码的文件...")
        file_layout.addWidget(self.file_path)

        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_file)
        file_layout.addWidget(self.browse_btn)

        layout.addWidget(file_group)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.encode_file_btn = QPushButton("🧬 编码文件为DNA")
        self.encode_file_btn.clicked.connect(self.encode_file)
        button_layout.addWidget(self.encode_file_btn)

        self.decode_file_btn = QPushButton("💾 解码DNA为文件")
        self.decode_file_btn.clicked.connect(self.decode_to_file)
        button_layout.addWidget(self.decode_file_btn)

        layout.addLayout(button_layout)

        # DNA序列输出区域
        dna_group = QGroupBox("DNA序列")
        dna_layout = QVBoxLayout(dna_group)

        self.file_dna_output = QTextEdit()
        self.file_dna_output.setReadOnly(True)
        self.file_dna_output.setFont(QFont("Courier", 10))
        dna_layout.addWidget(self.file_dna_output)

        layout.addWidget(dna_group)

        # 文件分析结果
        analysis_group = QGroupBox("文件分析")
        analysis_layout = QVBoxLayout(analysis_group)

        self.file_analysis = QTextEdit()
        self.file_analysis.setReadOnly(True)
        analysis_layout.addWidget(self.file_analysis)

        layout.addWidget(analysis_group)

        # 进度条
        self.file_progress_bar = QProgressBar()
        self.file_progress_bar.setVisible(False)
        layout.addWidget(self.file_progress_bar)

        return widget

    def create_settings_tab(self):
        """创建设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 编码设置
        encoding_group = QGroupBox("编码设置")
        encoding_layout = QVBoxLayout(encoding_group)

        # 最大序列长度
        length_layout = QHBoxLayout()
        length_label = QLabel("最大序列长度:")
        length_layout.addWidget(length_label)

        self.max_length_spin = QSpinBox()
        self.max_length_spin.setRange(50, 1000)
        self.max_length_spin.setValue(200)
        self.max_length_spin.setSingleStep(10)
        self.max_length_spin.valueChanged.connect(self.update_settings)
        length_layout.addWidget(self.max_length_spin)

        encoding_layout.addLayout(length_layout)

        # 错误校正
        self.error_correction_check = QCheckBox("启用错误校正 (MD5校验)")
        self.error_correction_check.setChecked(True)
        self.error_correction_check.stateChanged.connect(self.update_settings)
        encoding_layout.addWidget(self.error_correction_check)

        layout.addWidget(encoding_group)

        # 编码映射表
        mapping_group = QGroupBox("编码映射")
        mapping_layout = QVBoxLayout(mapping_group)

        mapping_text = QTextEdit()
        mapping_text.setReadOnly(True)
        mapping_text.setMaximumHeight(150)
        mapping_text.setFont(QFont("Courier", 10))
        mapping_text.setText("二进制 -> DNA碱基映射:\n\n" +
                            "00 -> A (腺嘌呤)\n" +
                            "01 -> T (胸腺嘧啶)\n" +
                            "10 -> G (鸟嘌呤)\n" +
                            "11 -> C (胞嘧啶)")
        mapping_layout.addWidget(mapping_text)

        layout.addWidget(mapping_group)

        # 关于信息
        about_group = QGroupBox("关于")
        about_layout = QVBoxLayout(about_group)

        about_text = QTextEdit()
        about_text.setReadOnly(True)
        about_text.setMaximumHeight(150)
        about_text.setText("DNA数据存储编码器 v1.0\n\n" +
                          "这个应用程序将数字数据转换为DNA序列，用于DNA数据存储研究。\n\n" +
                          "DNA数据存储是一种新兴的存储技术，利用DNA分子的特性来存储数字信息。\n" +
                          "每个DNA碱基可以编码2位二进制数据，理论上1克DNA可以存储215PB的数据。")
        about_layout.addWidget(about_text)

        layout.addWidget(about_group)

        return widget

    def update_settings(self):
        """更新编码器设置"""
        max_length = self.max_length_spin.value()
        error_correction = self.error_correction_check.isChecked()
        self.encoder = DNAEncoder(max_length=max_length, add_error_correction=error_correction)

    def encode_text(self):
        """编码文本为DNA"""
        text = self.text_input.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请输入要编码的文本！")
            return

        try:
            data = text.encode('utf-8')
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 无限进度条

            # 创建编码线程
            self.encode_thread = EncodeThread(self.encoder, data)
            self.encode_thread.finished.connect(self.on_encode_finished)
            self.encode_thread.error.connect(self.on_encode_error)
            self.encode_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"编码失败: {str(e)}")

    def on_encode_finished(self, sequences, analysis):
        """编码完成回调"""
        self.current_sequences = sequences
        self.progress_bar.setVisible(False)

        # 显示DNA序列
        dna_text = ""
        for i, seq in enumerate(sequences):
            dna_text += f"序列 {i+1}:\n{seq}\n\n"

        # 添加完整序列
        full_sequence = ''.join(sequences)
        dna_text += f"完整DNA序列:\n{full_sequence}"

        self.dna_output.setText(dna_text)

        # 显示分析结果
        analysis_text = f"序列分析结果:\n\n"
        analysis_text += f"序列数量: {analysis['sequence_count']}\n"
        analysis_text += f"总长度: {analysis['total_length']} 碱基\n"
        analysis_text += f"平均长度: {analysis['average_length']:.1f} 碱基\n"
        analysis_text += f"GC含量: {analysis['average_gc_content']:.2%}\n"
        analysis_text += f"GC含量范围: {analysis['gc_content_range'][0]:.2%} - {analysis['gc_content_range'][1]:.2%}"

        self.analysis_output.setText(analysis_text)

    def on_encode_error(self, error_msg):
        """编码错误回调"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "编码错误", f"编码失败: {error_msg}")

    def decode_dna(self):
        """解码DNA为文本"""
        if not self.current_sequences:
            QMessageBox.warning(self, "警告", "没有可解码的DNA序列！请先进行编码。")
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)

            # 创建解码线程
            self.decode_thread = DecodeThread(self.encoder, self.current_sequences)
            self.decode_thread.finished.connect(self.on_decode_finished)
            self.decode_thread.error.connect(self.on_decode_error)
            self.decode_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"解码失败: {str(e)}")

    def on_decode_finished(self, data):
        """解码完成回调"""
        self.progress_bar.setVisible(False)
        try:
            decoded_text = data.decode('utf-8')
            QMessageBox.information(self, "解码成功", f"解码结果:\n\n{decoded_text}")
        except UnicodeDecodeError:
            QMessageBox.information(self, "解码成功", f"解码结果 (二进制数据):\n\n{data}")

    def on_decode_error(self, error_msg):
        """解码错误回调"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "解码错误", f"解码失败: {error_msg}")

    def clear_all(self):
        """清空所有内容"""
        self.text_input.clear()
        self.dna_output.clear()
        self.analysis_output.clear()
        self.current_sequences = []

    def browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择文件", "", "所有文件 (*.*)")
        if file_path:
            self.file_path.setText(file_path)

    def encode_file(self):
        """编码文件为DNA"""
        file_path = self.file_path.text().strip()
        if not file_path:
            QMessageBox.warning(self, "警告", "请选择要编码的文件！")
            return

        try:
            with open(file_path, 'rb') as f:
                data = f.read()

            self.file_progress_bar.setVisible(True)
            self.file_progress_bar.setRange(0, 0)

            # 创建编码线程
            self.file_encode_thread = EncodeThread(self.encoder, data)
            self.file_encode_thread.finished.connect(self.on_file_encode_finished)
            self.file_encode_thread.error.connect(self.on_file_encode_error)
            self.file_encode_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取文件失败: {str(e)}")

    def on_file_encode_finished(self, sequences, analysis):
        """文件编码完成回调"""
        self.current_file_sequences = sequences
        self.file_progress_bar.setVisible(False)

        # 显示DNA序列
        dna_text = ""
        for i, seq in enumerate(sequences):
            dna_text += f"序列 {i+1}:\n{seq}\n\n"

        self.file_dna_output.setText(dna_text)

        # 显示分析结果
        analysis_text = f"文件编码分析:\n\n"
        analysis_text += f"序列数量: {analysis['sequence_count']}\n"
        analysis_text += f"总长度: {analysis['total_length']} 碱基\n"
        analysis_text += f"平均长度: {analysis['average_length']:.1f} 碱基\n"
        analysis_text += f"GC含量: {analysis['average_gc_content']:.2%}\n"

        self.file_analysis.setText(analysis_text)

    def on_file_encode_error(self, error_msg):
        """文件编码错误回调"""
        self.file_progress_bar.setVisible(False)
        QMessageBox.critical(self, "编码错误", f"文件编码失败: {error_msg}")

    def decode_to_file(self):
        """解码DNA为文件"""
        if not hasattr(self, 'current_file_sequences') or not self.current_file_sequences:
            QMessageBox.warning(self, "警告", "没有可解码的DNA序列！请先进行文件编码。")
            return

        save_path, _ = QFileDialog.getSaveFileName(self, "保存解码文件", "", "所有文件 (*.*)")
        if not save_path:
            return

        try:
            self.file_progress_bar.setVisible(True)
            self.file_progress_bar.setRange(0, 0)

            # 解码数据
            data = self.encoder.decode_data(self.current_file_sequences)

            # 保存文件
            with open(save_path, 'wb') as f:
                f.write(data)

            self.file_progress_bar.setVisible(False)
            QMessageBox.information(self, "成功", f"文件已成功解码并保存到:\n{save_path}")

        except Exception as e:
            self.file_progress_bar.setVisible(False)
            QMessageBox.critical(self, "错误", f"解码文件失败: {str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    # 创建主窗口
    window = DNAEncoderGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
