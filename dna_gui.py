#!/usr/bin/env python3
"""
DNA数据存储编码器 - 图形界面版本
使用PyQt5创建用户友好的界面
"""

import sys
import hashlib
import random
import math
from typing import List, Dict, Set, Tuple
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QTextEdit, QPushButton, QLabel,
                             QLineEdit, QCheckBox, QSpinBox, QTabWidget,
                             QGroupBox, QProgressBar, QMessageBox, QFileDialog,
                             QSplitter, QFrame, QSlider)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QTextCharFormat, QColor

class FountainCode:
    """喷泉码实现 - 用于DNA存储的纠删码"""

    def __init__(self, block_size: int = 64, redundancy: float = 1.5):
        """
        初始化喷泉码

        Args:
            block_size: 每个数据块的大小（字节）
            redundancy: 冗余度，生成的编码块数量 = 原始块数量 * redundancy
        """
        self.block_size = block_size
        self.redundancy = redundancy
        self.seed_base = 12345

    def encode(self, data: bytes) -> List[Dict]:
        """
        使用喷泉码编码数据

        Args:
            data: 要编码的原始数据

        Returns:
            编码块列表，每个块包含 {'id', 'data', 'degree', 'neighbors'}
        """
        # 1. 将数据分割为固定大小的块
        blocks = self._split_data(data)
        num_blocks = len(blocks)

        # 2. 计算需要生成的编码块数量
        num_encoded_blocks = int(num_blocks * self.redundancy)

        # 3. 生成编码块
        encoded_blocks = []
        for i in range(num_encoded_blocks):
            encoded_block = self._generate_encoded_block(blocks, i)
            encoded_blocks.append(encoded_block)

        return encoded_blocks

    def decode(self, encoded_blocks: List[Dict], original_size: int) -> bytes:
        """
        使用喷泉码解码数据

        Args:
            encoded_blocks: 编码块列表
            original_size: 原始数据大小

        Returns:
            解码后的原始数据
        """
        # 计算原始块数量
        num_original_blocks = math.ceil(original_size / self.block_size)

        # 使用高斯消元法解码
        decoded_blocks = self._gaussian_elimination(encoded_blocks, num_original_blocks)

        # 重组数据
        result = b''.join(decoded_blocks)
        return result[:original_size]  # 截取到原始大小

    def _split_data(self, data: bytes) -> List[bytes]:
        """将数据分割为固定大小的块"""
        blocks = []
        for i in range(0, len(data), self.block_size):
            block = data[i:i + self.block_size]
            # 如果最后一个块不足block_size，用0填充
            if len(block) < self.block_size:
                block += b'\x00' * (self.block_size - len(block))
            blocks.append(block)
        return blocks

    def _generate_encoded_block(self, blocks: List[bytes], seed: int) -> Dict:
        """生成一个编码块"""
        random.seed(self.seed_base + seed)
        num_blocks = len(blocks)

        # 使用鲁棒孤子分布选择度数
        degree = self._robust_soliton_degree(num_blocks)

        # 随机选择邻居块
        neighbors = random.sample(range(num_blocks), degree)

        # 对选中的块进行异或运算
        encoded_data = bytearray(self.block_size)
        for neighbor in neighbors:
            block_data = blocks[neighbor]
            for i in range(len(block_data)):
                encoded_data[i] ^= block_data[i]

        return {
            'id': seed,
            'data': bytes(encoded_data),
            'degree': degree,
            'neighbors': set(neighbors)
        }

    def _robust_soliton_degree(self, k: int) -> int:
        """鲁棒孤子分布 - 选择编码块的度数"""
        c = 0.1  # 常数参数
        delta = 0.5  # 失败概率

        # 计算参数
        R = c * math.log(k / delta) * math.sqrt(k)

        # 理想孤子分布
        def ideal_soliton(d):
            if d == 1:
                return 1.0 / k
            else:
                return 1.0 / (d * (d - 1))

        # 鲁棒孤子分布
        def robust_soliton(d):
            if d <= k / R:
                return ideal_soliton(d) + R / (d * k)
            elif d == k / R + 1:
                return ideal_soliton(d) + R * math.log(R / delta) / k
            else:
                return ideal_soliton(d)

        # 生成累积分布
        cumulative = []
        total = 0
        for d in range(1, k + 1):
            total += robust_soliton(d)
            cumulative.append(total)

        # 归一化
        cumulative = [x / total for x in cumulative]

        # 根据随机数选择度数
        r = random.random()
        for i, cum_prob in enumerate(cumulative):
            if r <= cum_prob:
                return i + 1

        return 1  # 默认返回1

    def _gaussian_elimination(self, encoded_blocks: List[Dict], num_original_blocks: int) -> List[bytes]:
        """使用高斯消元法解码"""
        # 初始化解码矩阵
        matrix = []
        for block in encoded_blocks:
            row = [0] * num_original_blocks
            for neighbor in block['neighbors']:
                if neighbor < num_original_blocks:
                    row[neighbor] = 1
            matrix.append((row, block['data']))

        # 高斯消元
        decoded = [None] * num_original_blocks

        # 前向消元
        for col in range(num_original_blocks):
            # 找到主元
            pivot_row = -1
            for row in range(len(matrix)):
                if matrix[row][0][col] == 1 and sum(matrix[row][0]) == 1:
                    pivot_row = row
                    break

            if pivot_row == -1:
                continue

            # 解出这一列
            decoded[col] = matrix[pivot_row][1]

            # 消除其他行中的这一列
            for row in range(len(matrix)):
                if row != pivot_row and matrix[row][0][col] == 1:
                    # 异或操作
                    new_data = bytearray(self.block_size)
                    for i in range(self.block_size):
                        new_data[i] = matrix[row][1][i] ^ decoded[col][i]
                    matrix[row] = (
                        [matrix[row][0][i] ^ matrix[pivot_row][0][i] for i in range(num_original_blocks)],
                        bytes(new_data)
                    )

            # 移除已使用的行
            matrix.pop(pivot_row)

        # 填充未解码的块
        for i in range(num_original_blocks):
            if decoded[i] is None:
                decoded[i] = b'\x00' * self.block_size

        return decoded

class DNAEncoder:
    """DNA数据存储编码器类"""

    # 基本编码映射：2位二进制 -> 1个DNA碱基
    BINARY_TO_DNA = {
        '00': 'A',
        '01': 'T',
        '10': 'G',
        '11': 'C'
    }

    # 解码映射：DNA碱基 -> 2位二进制
    DNA_TO_BINARY = {v: k for k, v in BINARY_TO_DNA.items()}

    # GC平衡的编码表（确保GC含量接近50%）
    GC_BALANCED_CODONS = {
        # 每个字节(8位)映射到3个碱基的密码子，确保GC含量平衡
        # 使用查找表方式，预先计算好GC平衡的编码
    }
    
    def __init__(self, max_length: int = 200, add_error_correction: bool = True,
                 use_fountain_code: bool = False, fountain_redundancy: float = 1.5):
        self.max_length = max_length
        self.add_error_correction = add_error_correction
        self.use_fountain_code = use_fountain_code
        self.fountain_code = FountainCode(block_size=32, redundancy=fountain_redundancy) if use_fountain_code else None
        self._init_gc_balanced_codons()
    
    def encode_data(self, data: bytes) -> List[str]:
        """将字节数据编码为DNA序列列表"""
        # 如果启用喷泉码，先进行喷泉码编码
        if self.use_fountain_code:
            return self._encode_with_fountain_code(data)
        else:
            return self._encode_traditional(data)

    def _encode_traditional(self, data: bytes) -> List[str]:
        """传统编码方法 - 使用GC平衡编码"""
        # 1. 添加数据长度信息
        original_length = len(data)
        length_bytes = original_length.to_bytes(4, 'big')

        # 2. 如果启用错误校正，添加校验和
        if self.add_error_correction:
            checksum = hashlib.md5(data).digest()
            combined_data = checksum + length_bytes + data
        else:
            combined_data = length_bytes + data

        # 3. 使用GC平衡编码转换为DNA序列
        dna_sequence = self._encode_gc_balanced(combined_data)

        # 4. 分割为多个序列
        return self._split_sequence(dna_sequence)

    def _init_gc_balanced_codons(self):
        """初始化GC平衡的编码表 - 使用简化的4位到2碱基映射"""
        # 使用4位二进制到2个DNA碱基的映射，确保GC平衡
        # 每个半字节(4位)映射到2个碱基，总共16种组合

        # 创建完整的16个2碱基对映射，确保GC平衡
        # 使用所有16种可能的2碱基组合，按GC含量排序
        all_pairs = []
        bases = ['A', 'T', 'G', 'C']

        for b1 in bases:
            for b2 in bases:
                pair = b1 + b2
                all_pairs.append(pair)

        # 按GC含量和重复风险排序
        def gc_content(pair):
            return pair.count('G') + pair.count('C')

        def repeat_risk(pair):
            # 相同碱基对有更高的重复风险
            if pair[0] == pair[1]:
                return 2
            return 0

        # 分类组合
        gc0_pairs = [p for p in all_pairs if gc_content(p) == 0]  # AA, AT, TA, TT
        gc1_pairs = [p for p in all_pairs if gc_content(p) == 1]  # AG, AC, TG, TC, GA, GT, CA, CT
        gc2_pairs = [p for p in all_pairs if gc_content(p) == 2]  # GG, GC, CG, CC

        # 在每个GC组内按重复风险排序（低风险优先）
        gc0_pairs.sort(key=repeat_risk)
        gc1_pairs.sort(key=repeat_risk)
        gc2_pairs.sort(key=repeat_risk)

        # 重新组合：优先使用GC含量为1的，然后是0和2的
        # 这样零值(最常见)会映射到低重复风险的组合
        ordered_pairs = gc1_pairs + gc0_pairs + gc2_pairs

        # 为4位二进制值(0-15)分配2碱基对
        self.nibble_to_pair = {}
        self.pair_to_nibble = {}

        for i in range(16):
            pair = ordered_pairs[i]
            self.nibble_to_pair[i] = pair
            self.pair_to_nibble[pair] = i

    def _encode_gc_balanced(self, data: bytes) -> str:
        """使用GC平衡编码将字节数据转换为DNA序列"""
        dna_sequence = ""

        for byte_val in data:
            # 将字节分成两个半字节（4位）
            high_nibble = (byte_val >> 4) & 0x0F
            low_nibble = byte_val & 0x0F

            # 直接使用映射表编码
            high_pair = self.nibble_to_pair[high_nibble]
            low_pair = self.nibble_to_pair[low_nibble]

            # 组合成4个碱基
            candidate = high_pair + low_pair
            dna_sequence += candidate

        return dna_sequence



    def _find_alternative_encoding(self, high_nibble: int, low_nibble: int, last_bases: str) -> str:
        """寻找替代编码以避免长重复 - 保持数据完整性"""
        # 重要：不能改变数据的含义，只能返回原始编码
        # 如果有重复问题，我们接受它，因为数据完整性更重要
        original_high_pair = self.nibble_to_pair[high_nibble]
        original_low_pair = self.nibble_to_pair[low_nibble]
        original_candidate = original_high_pair + original_low_pair

        # 总是返回原始编码以保持数据完整性
        return original_candidate

    def _would_create_long_repeat(self, last_bases: str, candidate: str) -> bool:
        """检查是否会产生连续4个或更多相同碱基"""
        combined = last_bases + candidate

        # 检查是否有连续4个相同碱基
        for i in range(len(combined) - 3):
            if len(set(combined[i:i+4])) == 1:  # 4个相同字符
                return True
        return False

    def _get_alternative_encoding(self, high_nibble: int, low_nibble: int, last_bases: str) -> str:
        """获取替代编码以避免长重复"""
        # 尝试不同的编码组合
        alternatives = []

        # 尝试所有可能的2碱基对组合
        for h_pair in self.nibble_to_pair.values():
            for l_pair in self.nibble_to_pair.values():
                candidate = h_pair + l_pair
                if not self._would_create_long_repeat(last_bases, candidate):
                    alternatives.append(candidate)

        # 如果找到替代方案，返回第一个
        if alternatives:
            return alternatives[0]

        # 如果没有找到，使用传统编码
        high_binary = format(high_nibble, '04b')
        low_binary = format(low_nibble, '04b')
        return (self.BINARY_TO_DNA[high_binary[:2]] + self.BINARY_TO_DNA[high_binary[2:]] +
                self.BINARY_TO_DNA[low_binary[:2]] + self.BINARY_TO_DNA[low_binary[2:]])

    def _get_alternative_codon(self, byte_val: int, last_three: str) -> str:
        """获取替代密码子，避免连续重复"""
        # 寻找相同字节值的其他密码子选项
        original_codon = self.byte_to_codon[byte_val]

        # 生成所有可能的密码子，找到不会产生连续重复的
        bases = ['A', 'T', 'G', 'C']
        for b1 in bases:
            for b2 in bases:
                for b3 in bases:
                    candidate = b1 + b2 + b3
                    gc_count = candidate.count('G') + candidate.count('C')

                    # 确保GC含量合理且不产生连续重复
                    if (gc_count in [1, 2] and
                        not (last_three[-1] == candidate[0] and
                             last_three[-2:] == candidate[0] * 2)):
                        return candidate

        # 如果找不到合适的，返回原始密码子
        return original_codon

    def _decode_gc_balanced(self, dna_sequence: str) -> bytes:
        """解码GC平衡编码的DNA序列"""
        data = bytearray()

        # 按4个碱基为一组解码（每个字节对应4个碱基）
        for i in range(0, len(dna_sequence), 4):
            quartet = dna_sequence[i:i+4]

            if len(quartet) == 4:
                # 分成两个2碱基对
                high_pair = quartet[:2]
                low_pair = quartet[2:]

                # 解码高半字节
                if high_pair in self.pair_to_nibble:
                    high_nibble = self.pair_to_nibble[high_pair]
                else:
                    # 回退到传统解码
                    high_binary = self.DNA_TO_BINARY.get(high_pair[0], '00') + self.DNA_TO_BINARY.get(high_pair[1], '00')
                    high_nibble = int(high_binary, 2)

                # 解码低半字节
                if low_pair in self.pair_to_nibble:
                    low_nibble = self.pair_to_nibble[low_pair]
                else:
                    # 回退到传统解码
                    low_binary = self.DNA_TO_BINARY.get(low_pair[0], '00') + self.DNA_TO_BINARY.get(low_pair[1], '00')
                    low_nibble = int(low_binary, 2)

                # 组合成字节：高半字节在高4位，低半字节在低4位
                byte_val = (high_nibble << 4) | (low_nibble & 0x0F)
                data.append(byte_val)

            elif len(quartet) >= 2:
                # 处理不完整的quartet
                for j in range(0, len(quartet), 2):
                    if j+1 < len(quartet):
                        pair = quartet[j:j+2]
                        if pair in self.pair_to_nibble:
                            nibble = self.pair_to_nibble[pair]
                            data.append(nibble)
                        else:
                            # 传统解码
                            binary = self.DNA_TO_BINARY.get(pair[0], '00') + self.DNA_TO_BINARY.get(pair[1], '00')
                            data.append(int(binary, 2))

        return bytes(data)

    def _encode_with_fountain_code(self, data: bytes) -> List[str]:
        """使用喷泉码的编码方法"""
        # 1. 使用喷泉码编码
        fountain_blocks = self.fountain_code.encode(data)

        # 2. 为每个喷泉码块创建DNA序列
        dna_sequences = []
        for block in fountain_blocks:
            # 创建块头信息
            header = {
                'original_size': len(data),
                'block_id': block['id'],
                'degree': block['degree'],
                'neighbors': list(block['neighbors'])
            }

            # 序列化头信息 - 使用更安全的格式
            neighbors_str = ','.join(map(str, sorted(block['neighbors']))) if block['neighbors'] else ''
            header_str = f"{len(data)}:{block['id']}:{block['degree']}:{neighbors_str}"
            header_bytes = header_str.encode('utf-8')

            # 组合头信息和数据
            combined_data = len(header_bytes).to_bytes(2, 'big') + header_bytes + block['data']

            # 使用GC平衡编码转换为DNA序列
            dna_sequence = self._encode_gc_balanced(combined_data)

            # 添加喷泉码标识前缀
            dna_sequence = "AAAA" + dna_sequence + "TTTT"  # 特殊标识

            dna_sequences.append(dna_sequence)

        return dna_sequences
    
    def decode_data(self, dna_sequences: List[str]) -> bytes:
        """将DNA序列列表解码为原始字节数据"""
        # 检查是否是喷泉码编码的序列
        if dna_sequences and dna_sequences[0].startswith("AAAA") and dna_sequences[0].endswith("TTTT"):
            return self._decode_fountain_code(dna_sequences)
        else:
            return self._decode_traditional(dna_sequences)

    def _decode_traditional(self, dna_sequences: List[str]) -> bytes:
        """传统解码方法 - 使用GC平衡解码"""
        # 1. 合并所有DNA序列
        full_dna = ''.join(dna_sequences)

        # 2. 使用GC平衡解码
        combined_data = self._decode_gc_balanced(full_dna)

        # 3. 提取校验和（如果有）
        if self.add_error_correction:
            if len(combined_data) < 16:
                raise ValueError("数据太短，无法提取校验和")
            checksum = combined_data[:16]  # MD5是16字节
            combined_data = combined_data[16:]

        # 4. 提取长度信息
        if len(combined_data) < 4:
            raise ValueError("数据太短，无法提取长度信息")

        original_length = int.from_bytes(combined_data[:4], 'big')
        data = combined_data[4:]

        # 5. 截取到原始长度
        if original_length > len(data):
            print(f"警告: 请求的长度 {original_length} 大于可用数据 {len(data)}，将使用所有可用数据")
            original_length = len(data)

        data = data[:original_length]

        # 6. 验证校验和
        if self.add_error_correction:
            expected_checksum = hashlib.md5(data).digest()
            if expected_checksum != checksum:
                raise ValueError("数据校验失败，可能存在错误")

        return data

    def _decode_fountain_code(self, dna_sequences: List[str]) -> bytes:
        """喷泉码解码方法"""
        fountain_blocks = []
        original_size = None

        for dna_seq in dna_sequences:
            # 移除喷泉码标识
            if dna_seq.startswith("AAAA") and dna_seq.endswith("TTTT"):
                dna_seq = dna_seq[4:-4]

            # 使用GC平衡解码
            combined_data = self._decode_gc_balanced(dna_seq)

            # 解析头信息
            try:
                if len(combined_data) < 2:
                    print(f"警告: 数据太短，无法解析头信息长度")
                    continue

                header_length = int.from_bytes(combined_data[:2], 'big')

                if len(combined_data) < 2 + header_length:
                    print(f"警告: 数据太短，无法解析完整头信息")
                    continue

                header_bytes = combined_data[2:2+header_length]
                block_data = combined_data[2+header_length:]

                # 解析头信息字符串
                header_str = header_bytes.decode('utf-8')
                parts = header_str.split(':')

                if len(parts) < 4:
                    print(f"警告: 头信息格式不正确: {header_str}")
                    continue

                if original_size is None:
                    original_size = int(parts[0])

                block_id = int(parts[1])
                degree = int(parts[2])
                neighbors = set(map(int, parts[3].split(','))) if parts[3] else set()

            except (UnicodeDecodeError, ValueError, IndexError) as e:
                # 如果解析失败，跳过这个块
                print(f"警告: 跳过无效的喷泉码块: {e}")
                continue

            fountain_blocks.append({
                'id': block_id,
                'data': block_data,
                'degree': degree,
                'neighbors': neighbors
            })

        # 使用喷泉码解码
        if original_size is None:
            raise ValueError("无法确定原始数据大小")

        return self.fountain_code.decode(fountain_blocks, original_size)
    
    def _binary_to_dna(self, binary_str: str) -> str:
        """将二进制字符串转换为DNA序列"""
        dna_sequence = ''
        for i in range(0, len(binary_str), 2):
            two_bits = binary_str[i:i+2]
            if len(two_bits) == 2:
                dna_sequence += self.BINARY_TO_DNA[two_bits]
        return dna_sequence
    
    def _dna_to_binary(self, dna_sequence: str) -> str:
        """将DNA序列转换为二进制字符串"""
        binary_str = ''
        for base in dna_sequence.upper():
            if base in self.DNA_TO_BINARY:
                binary_str += self.DNA_TO_BINARY[base]
        return binary_str
    
    def _split_sequence(self, dna_sequence: str) -> List[str]:
        """将长DNA序列分割为多个短序列"""
        sequences = []
        for i in range(0, len(dna_sequence), self.max_length):
            sequences.append(dna_sequence[i:i+self.max_length])
        return sequences
    
    def calculate_gc_content(self, dna_sequence: str) -> float:
        """计算GC含量"""
        gc_count = dna_sequence.count('G') + dna_sequence.count('C')
        return gc_count / len(dna_sequence) if len(dna_sequence) > 0 else 0
    
    def analyze_sequences(self, dna_sequences: List[str]) -> dict:
        """分析DNA序列的特性"""
        total_length = sum(len(seq) for seq in dna_sequences)
        gc_contents = [self.calculate_gc_content(seq) for seq in dna_sequences]
        
        return {
            'sequence_count': len(dna_sequences),
            'total_length': total_length,
            'average_length': total_length / len(dna_sequences) if dna_sequences else 0,
            'average_gc_content': sum(gc_contents) / len(gc_contents) if gc_contents else 0,
            'gc_content_range': (min(gc_contents), max(gc_contents)) if gc_contents else (0, 0)
        }

class EncodeThread(QThread):
    """编码线程"""
    finished = pyqtSignal(list, dict)
    error = pyqtSignal(str)
    
    def __init__(self, encoder, data):
        super().__init__()
        self.encoder = encoder
        self.data = data
    
    def run(self):
        try:
            sequences = self.encoder.encode_data(self.data)
            analysis = self.encoder.analyze_sequences(sequences)
            self.finished.emit(sequences, analysis)
        except Exception as e:
            self.error.emit(str(e))

class DecodeThread(QThread):
    """解码线程"""
    finished = pyqtSignal(bytes)
    error = pyqtSignal(str)
    
    def __init__(self, encoder, sequences):
        super().__init__()
        self.encoder = encoder
        self.sequences = sequences
    
    def run(self):
        try:
            data = self.encoder.decode_data(self.sequences)
            self.finished.emit(data)
        except Exception as e:
            self.error.emit(str(e))

class DNAEncoderGUI(QMainWindow):
    """DNA编码器图形界面"""
    
    def __init__(self):
        super().__init__()
        self.encoder = DNAEncoder()
        self.current_sequences = []
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("DNA数据存储编码器")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题
        title_label = QLabel("🧬 DNA数据存储编码器")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # 文本编码选项卡
        text_tab = self.create_text_tab()
        tab_widget.addTab(text_tab, "文本编码")
        
        # 文件编码选项卡
        file_tab = self.create_file_tab()
        tab_widget.addTab(file_tab, "文件编码")
        
        # 设置选项卡
        settings_tab = self.create_settings_tab()
        tab_widget.addTab(settings_tab, "设置")
    
    def create_text_tab(self):
        """创建文本编码选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 输入区域
        input_group = QGroupBox("输入文本")
        input_layout = QVBoxLayout(input_group)
        
        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText("请输入要编码的文本...")
        self.text_input.setMaximumHeight(100)
        input_layout.addWidget(self.text_input)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.encode_btn = QPushButton("🧬 编码为DNA")
        self.encode_btn.clicked.connect(self.encode_text)
        button_layout.addWidget(self.encode_btn)
        
        self.decode_btn = QPushButton("📝 解码为文本")
        self.decode_btn.clicked.connect(self.decode_dna)
        button_layout.addWidget(self.decode_btn)
        
        self.clear_btn = QPushButton("🗑️ 清空")
        self.clear_btn.clicked.connect(self.clear_all)
        button_layout.addWidget(self.clear_btn)
        
        input_layout.addLayout(button_layout)
        layout.addWidget(input_group)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # DNA序列输出区域
        dna_group = QGroupBox("DNA序列")
        dna_layout = QVBoxLayout(dna_group)
        
        self.dna_output = QTextEdit()
        self.dna_output.setReadOnly(True)
        self.dna_output.setFont(QFont("Courier", 10))
        dna_layout.addWidget(self.dna_output)
        
        splitter.addWidget(dna_group)
        
        # 分析结果区域
        analysis_group = QGroupBox("序列分析")
        analysis_layout = QVBoxLayout(analysis_group)
        
        self.analysis_output = QTextEdit()
        self.analysis_output.setReadOnly(True)
        self.analysis_output.setMaximumWidth(300)
        analysis_layout.addWidget(self.analysis_output)
        
        splitter.addWidget(analysis_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return widget

    def create_file_tab(self):
        """创建文件编码选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QHBoxLayout(file_group)

        self.file_path = QLineEdit()
        self.file_path.setReadOnly(True)
        self.file_path.setPlaceholderText("请选择要编码的文件...")
        file_layout.addWidget(self.file_path)

        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_file)
        file_layout.addWidget(self.browse_btn)

        layout.addWidget(file_group)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.encode_file_btn = QPushButton("🧬 编码文件为DNA")
        self.encode_file_btn.clicked.connect(self.encode_file)
        button_layout.addWidget(self.encode_file_btn)

        self.decode_file_btn = QPushButton("💾 解码DNA为文件")
        self.decode_file_btn.clicked.connect(self.decode_to_file)
        button_layout.addWidget(self.decode_file_btn)

        layout.addLayout(button_layout)

        # DNA序列输出区域
        dna_group = QGroupBox("DNA序列")
        dna_layout = QVBoxLayout(dna_group)

        self.file_dna_output = QTextEdit()
        self.file_dna_output.setReadOnly(True)
        self.file_dna_output.setFont(QFont("Courier", 10))
        dna_layout.addWidget(self.file_dna_output)

        layout.addWidget(dna_group)

        # 文件分析结果
        analysis_group = QGroupBox("文件分析")
        analysis_layout = QVBoxLayout(analysis_group)

        self.file_analysis = QTextEdit()
        self.file_analysis.setReadOnly(True)
        analysis_layout.addWidget(self.file_analysis)

        layout.addWidget(analysis_group)

        # 进度条
        self.file_progress_bar = QProgressBar()
        self.file_progress_bar.setVisible(False)
        layout.addWidget(self.file_progress_bar)

        return widget

    def create_settings_tab(self):
        """创建设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 编码设置
        encoding_group = QGroupBox("编码设置")
        encoding_layout = QVBoxLayout(encoding_group)

        # 最大序列长度
        length_layout = QHBoxLayout()
        length_label = QLabel("最大序列长度:")
        length_layout.addWidget(length_label)

        self.max_length_spin = QSpinBox()
        self.max_length_spin.setRange(50, 1000)
        self.max_length_spin.setValue(200)
        self.max_length_spin.setSingleStep(10)
        self.max_length_spin.valueChanged.connect(self.update_settings)
        length_layout.addWidget(self.max_length_spin)

        encoding_layout.addLayout(length_layout)

        # 错误校正
        self.error_correction_check = QCheckBox("启用错误校正 (MD5校验)")
        self.error_correction_check.setChecked(True)
        self.error_correction_check.stateChanged.connect(self.update_settings)
        encoding_layout.addWidget(self.error_correction_check)

        # 喷泉码选项
        self.fountain_code_check = QCheckBox("启用喷泉码 (高级纠删码)")
        self.fountain_code_check.setChecked(False)
        self.fountain_code_check.stateChanged.connect(self.update_settings)
        encoding_layout.addWidget(self.fountain_code_check)

        # 喷泉码冗余度
        redundancy_layout = QHBoxLayout()
        redundancy_label = QLabel("喷泉码冗余度:")
        redundancy_layout.addWidget(redundancy_label)

        self.redundancy_slider = QSlider(Qt.Horizontal)
        self.redundancy_slider.setRange(110, 300)  # 1.1 到 3.0
        self.redundancy_slider.setValue(150)  # 默认1.5
        self.redundancy_slider.valueChanged.connect(self.update_redundancy_label)
        self.redundancy_slider.valueChanged.connect(self.update_settings)
        redundancy_layout.addWidget(self.redundancy_slider)

        self.redundancy_value_label = QLabel("1.5")
        redundancy_layout.addWidget(self.redundancy_value_label)

        encoding_layout.addLayout(redundancy_layout)

        layout.addWidget(encoding_group)

        # 编码映射表
        mapping_group = QGroupBox("编码映射")
        mapping_layout = QVBoxLayout(mapping_group)

        mapping_text = QTextEdit()
        mapping_text.setReadOnly(True)
        mapping_text.setMaximumHeight(150)
        mapping_text.setFont(QFont("Courier", 10))
        mapping_text.setText("二进制 -> DNA碱基映射:\n\n" +
                            "00 -> A (腺嘌呤)\n" +
                            "01 -> T (胸腺嘧啶)\n" +
                            "10 -> G (鸟嘌呤)\n" +
                            "11 -> C (胞嘧啶)")
        mapping_layout.addWidget(mapping_text)

        layout.addWidget(mapping_group)

        # 关于信息
        about_group = QGroupBox("关于")
        about_layout = QVBoxLayout(about_group)

        about_text = QTextEdit()
        about_text.setReadOnly(True)
        about_text.setMaximumHeight(150)
        about_text.setText("DNA数据存储编码器 v2.0 (支持喷泉码)\n\n" +
                          "这个应用程序将数字数据转换为DNA序列，用于DNA数据存储研究。\n\n" +
                          "特性:\n" +
                          "• 传统编码: 基本的二进制到DNA转换\n" +
                          "• 喷泉码: 高级纠删码，提供优秀的数据恢复能力\n" +
                          "• MD5校验: 数据完整性验证\n" +
                          "• 可调参数: 序列长度、冗余度等\n\n" +
                          "DNA数据存储是一种新兴的存储技术，利用DNA分子的特性来存储数字信息。\n" +
                          "每个DNA碱基可以编码2位二进制数据，理论上1克DNA可以存储215PB的数据。\n\n" +
                          "喷泉码是一种无率码，即使部分DNA序列丢失或损坏，也能恢复原始数据。")
        about_layout.addWidget(about_text)

        layout.addWidget(about_group)

        return widget

    def update_settings(self):
        """更新编码器设置"""
        max_length = self.max_length_spin.value()
        error_correction = self.error_correction_check.isChecked()
        use_fountain_code = self.fountain_code_check.isChecked()
        fountain_redundancy = self.redundancy_slider.value() / 100.0

        self.encoder = DNAEncoder(
            max_length=max_length,
            add_error_correction=error_correction,
            use_fountain_code=use_fountain_code,
            fountain_redundancy=fountain_redundancy
        )

    def update_redundancy_label(self):
        """更新冗余度标签"""
        value = self.redundancy_slider.value() / 100.0
        self.redundancy_value_label.setText(f"{value:.1f}")

    def encode_text(self):
        """编码文本为DNA"""
        text = self.text_input.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请输入要编码的文本！")
            return

        try:
            data = text.encode('utf-8')
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 无限进度条

            # 创建编码线程
            self.encode_thread = EncodeThread(self.encoder, data)
            self.encode_thread.finished.connect(self.on_encode_finished)
            self.encode_thread.error.connect(self.on_encode_error)
            self.encode_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"编码失败: {str(e)}")

    def on_encode_finished(self, sequences, analysis):
        """编码完成回调"""
        self.current_sequences = sequences
        self.progress_bar.setVisible(False)

        # 显示DNA序列
        dna_text = ""
        for i, seq in enumerate(sequences):
            dna_text += f"序列 {i+1}:\n{seq}\n\n"

        # 添加完整序列
        full_sequence = ''.join(sequences)
        dna_text += f"完整DNA序列:\n{full_sequence}"

        self.dna_output.setText(dna_text)

        # 显示分析结果
        analysis_text = f"序列分析结果:\n\n"
        analysis_text += f"编码方式: {'喷泉码' if self.encoder.use_fountain_code else '传统编码'}\n"
        if self.encoder.use_fountain_code:
            analysis_text += f"冗余度: {self.encoder.fountain_code.redundancy:.1f}\n"
        analysis_text += f"序列数量: {analysis['sequence_count']}\n"
        analysis_text += f"总长度: {analysis['total_length']} 碱基\n"
        analysis_text += f"平均长度: {analysis['average_length']:.1f} 碱基\n"
        analysis_text += f"GC含量: {analysis['average_gc_content']:.2%}\n"
        analysis_text += f"GC含量范围: {analysis['gc_content_range'][0]:.2%} - {analysis['gc_content_range'][1]:.2%}"

        self.analysis_output.setText(analysis_text)

    def on_encode_error(self, error_msg):
        """编码错误回调"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "编码错误", f"编码失败: {error_msg}")

    def decode_dna(self):
        """解码DNA为文本"""
        if not self.current_sequences:
            QMessageBox.warning(self, "警告", "没有可解码的DNA序列！请先进行编码。")
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)

            # 创建解码线程
            self.decode_thread = DecodeThread(self.encoder, self.current_sequences)
            self.decode_thread.finished.connect(self.on_decode_finished)
            self.decode_thread.error.connect(self.on_decode_error)
            self.decode_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"解码失败: {str(e)}")

    def on_decode_finished(self, data):
        """解码完成回调"""
        self.progress_bar.setVisible(False)
        try:
            decoded_text = data.decode('utf-8')
            QMessageBox.information(self, "解码成功", f"解码结果:\n\n{decoded_text}")
        except UnicodeDecodeError:
            QMessageBox.information(self, "解码成功", f"解码结果 (二进制数据):\n\n{data}")

    def on_decode_error(self, error_msg):
        """解码错误回调"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "解码错误", f"解码失败: {error_msg}")

    def clear_all(self):
        """清空所有内容"""
        self.text_input.clear()
        self.dna_output.clear()
        self.analysis_output.clear()
        self.current_sequences = []

    def browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择文件", "", "所有文件 (*.*)")
        if file_path:
            self.file_path.setText(file_path)

    def encode_file(self):
        """编码文件为DNA"""
        file_path = self.file_path.text().strip()
        if not file_path:
            QMessageBox.warning(self, "警告", "请选择要编码的文件！")
            return

        try:
            with open(file_path, 'rb') as f:
                data = f.read()

            self.file_progress_bar.setVisible(True)
            self.file_progress_bar.setRange(0, 0)

            # 创建编码线程
            self.file_encode_thread = EncodeThread(self.encoder, data)
            self.file_encode_thread.finished.connect(self.on_file_encode_finished)
            self.file_encode_thread.error.connect(self.on_file_encode_error)
            self.file_encode_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取文件失败: {str(e)}")

    def on_file_encode_finished(self, sequences, analysis):
        """文件编码完成回调"""
        self.current_file_sequences = sequences
        self.file_progress_bar.setVisible(False)

        # 显示DNA序列
        dna_text = ""
        for i, seq in enumerate(sequences):
            dna_text += f"序列 {i+1}:\n{seq}\n\n"

        self.file_dna_output.setText(dna_text)

        # 显示分析结果
        analysis_text = f"文件编码分析:\n\n"
        analysis_text += f"序列数量: {analysis['sequence_count']}\n"
        analysis_text += f"总长度: {analysis['total_length']} 碱基\n"
        analysis_text += f"平均长度: {analysis['average_length']:.1f} 碱基\n"
        analysis_text += f"GC含量: {analysis['average_gc_content']:.2%}\n"

        self.file_analysis.setText(analysis_text)

    def on_file_encode_error(self, error_msg):
        """文件编码错误回调"""
        self.file_progress_bar.setVisible(False)
        QMessageBox.critical(self, "编码错误", f"文件编码失败: {error_msg}")

    def decode_to_file(self):
        """解码DNA为文件"""
        if not hasattr(self, 'current_file_sequences') or not self.current_file_sequences:
            QMessageBox.warning(self, "警告", "没有可解码的DNA序列！请先进行文件编码。")
            return

        save_path, _ = QFileDialog.getSaveFileName(self, "保存解码文件", "", "所有文件 (*.*)")
        if not save_path:
            return

        try:
            self.file_progress_bar.setVisible(True)
            self.file_progress_bar.setRange(0, 0)

            # 解码数据
            data = self.encoder.decode_data(self.current_file_sequences)

            # 保存文件
            with open(save_path, 'wb') as f:
                f.write(data)

            self.file_progress_bar.setVisible(False)
            QMessageBox.information(self, "成功", f"文件已成功解码并保存到:\n{save_path}")

        except Exception as e:
            self.file_progress_bar.setVisible(False)
            QMessageBox.critical(self, "错误", f"解码文件失败: {str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    # 创建主窗口
    window = DNAEncoderGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
