#!/usr/bin/env python3
"""
测试长文本转换为AGCT
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder

def test_long_chinese_text():
    """测试长中文文本"""
    print("=== 长中文文本DNA编码 ===\n")
    
    # 创建编码器
    encoder = DNAEncoder(max_length=200, add_error_correction=True, use_fountain_code=False)
    
    # 测试文本
    test_text = "中国科学技术大学生命科学学院实验记录本实验室课题名称使用人起止时间"
    test_data = test_text.encode('utf-8')
    
    print(f"原始文本: {test_text}")
    print(f"文本长度: {len(test_text)} 字符")
    print(f"数据长度: {len(test_data)} 字节")
    
    # 编码
    dna_sequences = encoder.encode_data(test_data)
    
    print(f"\nDNA序列数量: {len(dna_sequences)}")
    total_gc = 0
    total_length = 0
    max_repeat_overall = 0
    
    for i, seq in enumerate(dna_sequences):
        print(f"\n序列 {i+1}:")
        print(f"  {seq}")
        
        # 计算GC含量
        gc_count = seq.count('G') + seq.count('C')
        gc_percentage = (gc_count / len(seq)) * 100
        total_gc += gc_count
        total_length += len(seq)
        
        print(f"  长度: {len(seq)} 碱基")
        print(f"  GC含量: {gc_percentage:.1f}%")
        
        # 检查连续重复
        max_repeat = check_consecutive_repeats(seq)
        max_repeat_overall = max(max_repeat_overall, max_repeat)
        print(f"  最长连续重复: {max_repeat}")
    
    # 合并所有序列
    full_dna = ''.join(dna_sequences)
    print(f"\n=== 完整DNA序列 ===")
    print(f"完整序列: {full_dna}")
    
    overall_gc = (total_gc / total_length) * 100 if total_length > 0 else 0
    print(f"\n=== 总体统计 ===")
    print(f"总体GC含量: {overall_gc:.1f}%")
    print(f"总长度: {total_length} 碱基")
    print(f"最长连续重复: {max_repeat_overall}")
    print(f"GC含量是否在理想范围(40-60%): {'是' if 40 <= overall_gc <= 60 else '否'}")
    print(f"连续重复是否控制良好(<4): {'是' if max_repeat_overall < 4 else '否'}")
    
    # 解码验证
    try:
        decoded_data = encoder.decode_data(dna_sequences)
        decoded_text = decoded_data.decode('utf-8')
        print(f"\n=== 解码验证 ===")
        print(f"解码文本: {decoded_text}")
        print(f"编码解码成功: {decoded_text == test_text}")
        
        if decoded_text != test_text:
            print(f"原始长度: {len(test_text)}")
            print(f"解码长度: {len(decoded_text)}")
            
    except Exception as e:
        print(f"解码失败: {e}")
        import traceback
        traceback.print_exc()

def check_consecutive_repeats(sequence):
    """检查最长连续重复碱基数量"""
    if not sequence:
        return 0
    
    max_repeat = 1
    current_repeat = 1
    
    for i in range(1, len(sequence)):
        if sequence[i] == sequence[i-1]:
            current_repeat += 1
            max_repeat = max(max_repeat, current_repeat)
        else:
            current_repeat = 1
    
    return max_repeat

def main():
    """主函数"""
    test_long_chinese_text()

if __name__ == "__main__":
    main()
