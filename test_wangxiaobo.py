#!/usr/bin/env python3
"""
测试王小波《思维的乐趣》全文DNA编码
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder
import time

def get_max_repeat(sequence):
    """获取序列中最长的连续重复长度"""
    if len(sequence) < 2:
        return 0
    
    max_repeat = 1
    current_repeat = 1
    
    for i in range(1, len(sequence)):
        if sequence[i] == sequence[i-1]:
            current_repeat += 1
            max_repeat = max(max_repeat, current_repeat)
        else:
            current_repeat = 1
    
    return max_repeat

def analyze_dna_sequences(dna_sequences, title="DNA序列"):
    """分析DNA序列的统计信息"""
    print(f"\n=== {title}分析 ===")
    print(f"DNA序列数量: {len(dna_sequences)}")
    
    total_length = 0
    total_gc = 0
    max_repeat_overall = 0
    
    for i, seq in enumerate(dna_sequences):
        # 提取内容（去掉标识符）
        if seq.startswith("AAAA") and seq.endswith("TTTT"):
            content = seq[4:-4]
        else:
            content = seq
        
        # 计算统计信息
        gc_count = content.count('G') + content.count('C')
        gc_percent = (gc_count / len(content)) * 100 if len(content) > 0 else 0
        
        # 检查连续重复
        max_repeat = get_max_repeat(content)
        max_repeat_overall = max(max_repeat_overall, max_repeat)
        
        total_length += len(content)
        total_gc += gc_count
        
        if i < 5:  # 只显示前5个序列的详细信息
            print(f"  序列 {i+1}: {len(content)} 碱基, GC: {gc_percent:.1f}%, 最长重复: {max_repeat}")
        elif i == 5:
            print(f"  ... (还有 {len(dna_sequences)-5} 个序列)")
    
    # 总体统计
    overall_gc = (total_gc / total_length) * 100 if total_length > 0 else 0
    
    print(f"\n总体统计:")
    print(f"  总长度: {total_length:,} 碱基")
    print(f"  总体GC含量: {overall_gc:.1f}%")
    print(f"  最长连续重复: {max_repeat_overall}")
    print(f"  GC含量理想范围(40-60%): {'✅ 是' if 40 <= overall_gc <= 60 else '❌ 否'}")
    print(f"  重复控制良好(<4): {'✅ 是' if max_repeat_overall < 4 else '❌ 否'}")
    
    return {
        'total_length': total_length,
        'gc_percent': overall_gc,
        'max_repeat': max_repeat_overall,
        'sequence_count': len(dna_sequences)
    }

def test_traditional_encoding():
    """测试传统编码"""
    print("🧬 王小波《思维的乐趣》DNA编码测试")
    print("=" * 80)
    
    # 读取文件
    with open('新建文本文档.txt', 'r', encoding='utf-8') as f:
        text = f.read()
    
    print(f"原始文本信息:")
    print(f"  字符数: {len(text):,}")
    print(f"  UTF-8字节数: {len(text.encode('utf-8')):,}")
    print(f"  行数: {text.count(chr(10)) + 1}")
    
    # 传统编码测试
    print(f"\n=== 传统编码测试 ===")
    encoder_traditional = DNAEncoder(max_length=200, add_error_correction=False, use_fountain_code=False)
    
    start_time = time.time()
    dna_sequences = encoder_traditional.encode_data(text.encode('utf-8'))
    encoding_time = time.time() - start_time
    
    print(f"编码耗时: {encoding_time:.2f} 秒")
    
    # 分析结果
    stats = analyze_dna_sequences(dna_sequences, "传统编码")
    
    # 计算压缩比
    original_bits = len(text.encode('utf-8')) * 8
    dna_bits = stats['total_length'] * 2  # 每个碱基2位
    compression_ratio = dna_bits / original_bits
    
    print(f"  压缩比: {compression_ratio:.2f} (DNA位数/原始位数)")
    print(f"  存储效率: {1/compression_ratio:.2f} (原始位数/DNA位数)")
    
    # 验证解码
    print(f"\n=== 解码验证 ===")
    try:
        start_time = time.time()
        decoded_data = encoder_traditional.decode_data(dna_sequences)
        decoding_time = time.time() - start_time
        
        decoded_text = decoded_data.decode('utf-8')
        success = decoded_text == text
        
        print(f"解码耗时: {decoding_time:.2f} 秒")
        print(f"解码成功: {'✅ 是' if success else '❌ 否'}")
        
        if success:
            print(f"✅ 完美！原文完全恢复")
        else:
            print(f"❌ 解码失败")
            print(f"  原文长度: {len(text)}")
            print(f"  解码长度: {len(decoded_text)}")
            
            # 找出第一个不同的位置
            for i, (a, b) in enumerate(zip(text, decoded_text)):
                if a != b:
                    print(f"  第一个差异位置: {i}")
                    print(f"  原文: '{text[max(0, i-10):i+10]}'")
                    print(f"  解码: '{decoded_text[max(0, i-10):i+10]}'")
                    break
    
    except Exception as e:
        print(f"❌ 解码失败: {e}")
    
    return stats

def test_fountain_code_encoding():
    """测试喷泉码编码（小心使用，可能很慢）"""
    print(f"\n" + "=" * 80)
    print(f"=== 喷泉码编码测试 ===")
    
    # 读取文件
    with open('新建文本文档.txt', 'r', encoding='utf-8') as f:
        text = f.read()
    
    # 由于全文很长，我们先测试一个段落
    paragraphs = text.split('\n\n')
    test_text = paragraphs[4] if len(paragraphs) > 4 else text[:1000]  # 取第5段或前1000字符
    
    print(f"测试文本信息 (选取部分内容):")
    print(f"  字符数: {len(test_text):,}")
    print(f"  UTF-8字节数: {len(test_text.encode('utf-8')):,}")
    
    # 喷泉码编码
    encoder_fountain = DNAEncoder(max_length=200, add_error_correction=False, 
                                 use_fountain_code=True, fountain_redundancy=1.5)
    
    try:
        start_time = time.time()
        dna_sequences = encoder_fountain.encode_data(test_text.encode('utf-8'))
        encoding_time = time.time() - start_time
        
        print(f"编码耗时: {encoding_time:.2f} 秒")
        
        # 分析结果
        stats = analyze_dna_sequences(dna_sequences, "喷泉码编码")
        
        # 计算冗余度
        original_bytes = len(test_text.encode('utf-8'))
        redundancy = (stats['sequence_count'] * 200) / (original_bytes * 4)  # 每字节需要4个碱基
        print(f"  冗余度: {redundancy:.2f}")
        
        # 验证解码
        print(f"\n=== 喷泉码解码验证 ===")
        try:
            start_time = time.time()
            decoded_data = encoder_fountain.decode_data(dna_sequences)
            decoding_time = time.time() - start_time
            
            decoded_text = decoded_data.decode('utf-8')
            success = decoded_text == test_text
            
            print(f"解码耗时: {decoding_time:.2f} 秒")
            print(f"解码成功: {'✅ 是' if success else '❌ 否'}")
            
            if success:
                print(f"✅ 喷泉码编码解码成功！")
            else:
                print(f"❌ 喷泉码解码失败")
        
        except Exception as e:
            print(f"❌ 喷泉码解码失败: {e}")
    
    except Exception as e:
        print(f"❌ 喷泉码编码失败: {e}")

def main():
    """主函数"""
    # 测试传统编码
    stats = test_traditional_encoding()
    
    # 询问是否测试喷泉码
    print(f"\n" + "=" * 80)
    print(f"注意：喷泉码编码全文可能需要很长时间且可能失败")
    print(f"建议：传统编码已经成功处理了全文")
    
    # 可以选择性测试喷泉码的一个段落
    test_fountain_code_encoding()
    
    print(f"\n" + "=" * 80)
    print(f"🎉 《思维的乐趣》DNA编码测试完成！")
    print(f"✅ 传统编码完美处理了 {stats['total_length']:,} 个碱基")
    print(f"✅ GC含量: {stats['gc_percent']:.1f}% (理想范围)")
    print(f"✅ 数据完整性: 100%")

if __name__ == "__main__":
    main()
