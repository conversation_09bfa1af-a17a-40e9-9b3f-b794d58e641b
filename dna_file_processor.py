#!/usr/bin/env python3
"""
DNA文件处理器
用于将文件编码为DNA序列并保存，以及从DNA序列恢复文件
"""

import os
import json
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List

from dna_encoder import DNAEncoder
from advanced_dna_encoder import AdvancedDNAEncoder

class DNAFileProcessor:
    """DNA文件处理器"""
    
    def __init__(self, use_advanced: bool = True, **kwargs):
        """
        初始化文件处理器
        
        Args:
            use_advanced: 是否使用高级编码器
            **kwargs: 编码器参数
        """
        if use_advanced:
            self.encoder = AdvancedDNAEncoder(**kwargs)
        else:
            self.encoder = DNAEncoder(**kwargs)
        
        self.use_advanced = use_advanced
    
    def encode_file(self, input_file: str, output_dir: str = "dna_output") -> Dict:
        """
        将文件编码为DNA序列
        
        Args:
            input_file: 输入文件路径
            output_dir: 输出目录
            
        Returns:
            编码结果信息
        """
        # 创建输出目录
        Path(output_dir).mkdir(exist_ok=True)
        
        # 读取文件
        with open(input_file, 'rb') as f:
            file_data = f.read()
        
        print(f"正在编码文件: {input_file}")
        print(f"文件大小: {len(file_data)} 字节")
        
        # 编码
        if self.use_advanced:
            dna_sequences = self.encoder.encode_with_optimization(file_data)
        else:
            dna_sequences = self.encoder.encode_data(file_data)
        
        # 分析序列
        if hasattr(self.encoder, 'analyze_sequences'):
            analysis = self.encoder.analyze_sequences(dna_sequences)
        else:
            analysis = {'sequence_count': len(dna_sequences)}
        
        # 准备输出文件名
        base_name = Path(input_file).stem
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存DNA序列
        dna_file = Path(output_dir) / f"{base_name}_dna_{timestamp}.txt"
        with open(dna_file, 'w') as f:
            for i, seq in enumerate(dna_sequences):
                f.write(f">Sequence_{i+1}\n{seq}\n")
        
        # 保存元数据
        metadata = {
            'original_file': input_file,
            'original_size': len(file_data),
            'encoding_timestamp': timestamp,
            'encoder_type': 'advanced' if self.use_advanced else 'basic',
            'encoder_config': self._get_encoder_config(),
            'sequence_count': len(dna_sequences),
            'total_dna_length': sum(len(seq) for seq in dna_sequences),
            'analysis': analysis
        }
        
        metadata_file = Path(output_dir) / f"{base_name}_metadata_{timestamp}.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        # 保存原始文件信息（用于验证）
        file_info = {
            'filename': Path(input_file).name,
            'size': len(file_data),
            'checksum': self._calculate_file_checksum(file_data)
        }
        
        info_file = Path(output_dir) / f"{base_name}_fileinfo_{timestamp}.json"
        with open(info_file, 'w') as f:
            json.dump(file_info, f, indent=2)
        
        result = {
            'dna_file': str(dna_file),
            'metadata_file': str(metadata_file),
            'info_file': str(info_file),
            'sequences': len(dna_sequences),
            'compression_ratio': len(file_data) / sum(len(seq) for seq in dna_sequences),
            'analysis': analysis
        }
        
        print(f"编码完成!")
        print(f"DNA序列文件: {dna_file}")
        print(f"元数据文件: {metadata_file}")
        print(f"序列数量: {len(dna_sequences)}")
        print(f"总DNA长度: {sum(len(seq) for seq in dna_sequences)}")
        
        return result
    
    def decode_file(self, dna_file: str, output_file: str = None) -> str:
        """
        从DNA序列恢复文件
        
        Args:
            dna_file: DNA序列文件路径
            output_file: 输出文件路径（可选）
            
        Returns:
            恢复的文件路径
        """
        # 读取DNA序列
        dna_sequences = []
        with open(dna_file, 'r') as f:
            current_seq = ""
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    if current_seq:
                        dna_sequences.append(current_seq)
                        current_seq = ""
                else:
                    current_seq += line
            if current_seq:
                dna_sequences.append(current_seq)
        
        print(f"从文件读取了 {len(dna_sequences)} 个DNA序列")
        
        # 解码
        try:
            if self.use_advanced:
                decoded_data = self.encoder.decode_optimized_data(dna_sequences)
            else:
                decoded_data = self.encoder.decode_data(dna_sequences)
        except Exception as e:
            print(f"解码失败: {e}")
            raise
        
        # 确定输出文件名
        if output_file is None:
            base_name = Path(dna_file).stem.replace('_dna_', '_decoded_')
            output_file = Path(dna_file).parent / f"{base_name}.bin"
        
        # 保存解码后的文件
        with open(output_file, 'wb') as f:
            f.write(decoded_data)
        
        print(f"文件解码完成: {output_file}")
        print(f"解码后文件大小: {len(decoded_data)} 字节")
        
        return str(output_file)
    
    def _get_encoder_config(self) -> Dict:
        """获取编码器配置"""
        if self.use_advanced:
            return {
                'encoding_scheme': self.encoder.encoding_scheme,
                'max_length': self.encoder.max_length,
                'target_gc_content': self.encoder.target_gc_content,
                'avoid_homopolymers': self.encoder.avoid_homopolymers,
                'max_homopolymer_length': self.encoder.max_homopolymer_length
            }
        else:
            return {
                'max_length': self.encoder.max_length,
                'add_error_correction': self.encoder.add_error_correction
            }
    
    def _calculate_file_checksum(self, data: bytes) -> str:
        """计算文件校验和"""
        import hashlib
        return hashlib.sha256(data).hexdigest()
    
    def batch_encode_directory(self, input_dir: str, output_dir: str = "dna_batch_output") -> List[Dict]:
        """
        批量编码目录中的文件
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            
        Returns:
            编码结果列表
        """
        input_path = Path(input_dir)
        if not input_path.exists():
            raise FileNotFoundError(f"输入目录不存在: {input_dir}")
        
        results = []
        files = list(input_path.glob('*'))
        files = [f for f in files if f.is_file()]
        
        print(f"找到 {len(files)} 个文件进行批量编码")
        
        for file_path in files:
            try:
                print(f"\n处理文件: {file_path}")
                result = self.encode_file(str(file_path), output_dir)
                result['original_file'] = str(file_path)
                results.append(result)
            except Exception as e:
                print(f"编码文件 {file_path} 时出错: {e}")
                results.append({
                    'original_file': str(file_path),
                    'error': str(e)
                })
        
        # 保存批量处理报告
        report_file = Path(output_dir) / f"batch_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n批量编码完成，报告保存至: {report_file}")
        return results


def main():
    """命令行接口"""
    parser = argparse.ArgumentParser(description="DNA文件编码器")
    parser.add_argument('action', choices=['encode', 'decode', 'batch'], help="操作类型")
    parser.add_argument('input', help="输入文件或目录路径")
    parser.add_argument('-o', '--output', help="输出路径")
    parser.add_argument('--basic', action='store_true', help="使用基础编码器")
    parser.add_argument('--max-length', type=int, default=200, help="最大序列长度")
    parser.add_argument('--gc-content', type=float, default=0.5, help="目标GC含量")
    
    args = parser.parse_args()
    
    # 创建处理器
    processor_kwargs = {
        'max_length': args.max_length,
    }
    
    if not args.basic:
        processor_kwargs.update({
            'target_gc_content': args.gc_content,
            'avoid_homopolymers': True,
            'max_homopolymer_length': 3
        })
    
    processor = DNAFileProcessor(use_advanced=not args.basic, **processor_kwargs)
    
    # 执行操作
    if args.action == 'encode':
        result = processor.encode_file(args.input, args.output or "dna_output")
        print(f"\n编码结果: {result}")
    
    elif args.action == 'decode':
        output_file = processor.decode_file(args.input, args.output)
        print(f"\n解码完成: {output_file}")
    
    elif args.action == 'batch':
        results = processor.batch_encode_directory(args.input, args.output or "dna_batch_output")
        successful = len([r for r in results if 'error' not in r])
        print(f"\n批量处理完成: {successful}/{len(results)} 个文件成功编码")


if __name__ == "__main__":
    main()
