#!/usr/bin/env python3
"""
最终测试 - 人类是有极限的，我不做人啦
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder

def test_final_text():
    """测试最终文本"""
    print("=== 最终测试：人类是有极限的，我不做人啦 ===\n")
    
    # 创建编码器
    encoder = DNAEncoder(max_length=200, add_error_correction=True, use_fountain_code=False)
    
    # 测试文本
    test_text = "人类是有极限的，我不做人啦"
    test_data = test_text.encode('utf-8')
    
    print(f"原始文本: {test_text}")
    print(f"数据长度: {len(test_data)} 字节")
    
    # 编码
    dna_sequences = encoder.encode_data(test_data)
    
    print(f"\nDNA序列数量: {len(dna_sequences)}")
    total_gc = 0
    total_length = 0
    max_repeat_overall = 0
    
    for i, seq in enumerate(dna_sequences):
        print(f"\n序列 {i+1}:")
        print(f"  {seq}")
        
        # 计算GC含量
        gc_count = seq.count('G') + seq.count('C')
        gc_percentage = (gc_count / len(seq)) * 100
        total_gc += gc_count
        total_length += len(seq)
        
        print(f"  长度: {len(seq)} 碱基")
        print(f"  GC含量: {gc_percentage:.1f}%")
        
        # 检查连续重复
        max_repeat = check_consecutive_repeats(seq)
        max_repeat_overall = max(max_repeat_overall, max_repeat)
        print(f"  最长连续重复: {max_repeat}")
    
    overall_gc = (total_gc / total_length) * 100 if total_length > 0 else 0
    print(f"\n=== 总体统计 ===")
    print(f"总体GC含量: {overall_gc:.1f}%")
    print(f"总长度: {total_length} 碱基")
    print(f"最长连续重复: {max_repeat_overall}")
    print(f"GC含量是否在理想范围(40-60%): {'是' if 40 <= overall_gc <= 60 else '否'}")
    print(f"连续重复是否控制良好(<4): {'是' if max_repeat_overall < 4 else '否'}")
    
    # 解码验证
    try:
        decoded_data = encoder.decode_data(dna_sequences)
        decoded_text = decoded_data.decode('utf-8')
        print(f"\n=== 解码验证 ===")
        print(f"解码文本: {decoded_text}")
        print(f"编码解码成功: {decoded_text == test_text}")
    except Exception as e:
        print(f"解码失败: {e}")

def test_fountain_code_final():
    """测试喷泉码版本"""
    print("\n" + "="*60)
    print("=== 喷泉码测试：人类是有极限的，我不做人啦 ===\n")
    
    # 创建喷泉码编码器
    encoder = DNAEncoder(max_length=200, add_error_correction=True, 
                        use_fountain_code=True, fountain_redundancy=1.5)
    
    # 测试文本
    test_text = "人类是有极限的，我不做人啦"
    test_data = test_text.encode('utf-8')
    
    print(f"原始文本: {test_text}")
    print(f"数据长度: {len(test_data)} 字节")
    
    # 编码
    try:
        dna_sequences = encoder.encode_data(test_data)
        
        print(f"\n喷泉码DNA序列数量: {len(dna_sequences)}")
        total_gc = 0
        total_length = 0
        max_repeat_overall = 0
        
        for i, seq in enumerate(dna_sequences):
            print(f"\n序列 {i+1}:")
            print(f"  {seq}")
            
            # 分析内容部分（排除标识符）
            if seq.startswith("AAAA") and seq.endswith("TTTT"):
                content_seq = seq[4:-4]
            else:
                content_seq = seq
            
            # 计算GC含量
            gc_count = content_seq.count('G') + content_seq.count('C')
            gc_percentage = (gc_count / len(content_seq)) * 100 if content_seq else 0
            total_gc += gc_count
            total_length += len(content_seq)
            
            print(f"  内容长度: {len(content_seq)} 碱基")
            print(f"  GC含量: {gc_percentage:.1f}%")
            
            # 检查连续重复
            max_repeat = check_consecutive_repeats(content_seq)
            max_repeat_overall = max(max_repeat_overall, max_repeat)
            print(f"  最长连续重复: {max_repeat}")
        
        overall_gc = (total_gc / total_length) * 100 if total_length > 0 else 0
        print(f"\n=== 喷泉码总体统计 ===")
        print(f"总体GC含量: {overall_gc:.1f}%")
        print(f"总长度: {total_length} 碱基")
        print(f"最长连续重复: {max_repeat_overall}")
        print(f"冗余度: {encoder.fountain_code.redundancy}")
        
        # 解码验证
        try:
            decoded_data = encoder.decode_data(dna_sequences)
            decoded_text = decoded_data.decode('utf-8')
            print(f"\n=== 喷泉码解码验证 ===")
            print(f"解码文本: {decoded_text}")
            print(f"编码解码成功: {decoded_text == test_text}")
        except Exception as e:
            print(f"喷泉码解码失败: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"喷泉码编码失败: {e}")
        import traceback
        traceback.print_exc()

def check_consecutive_repeats(sequence):
    """检查最长连续重复碱基数量"""
    if not sequence:
        return 0
    
    max_repeat = 1
    current_repeat = 1
    
    for i in range(1, len(sequence)):
        if sequence[i] == sequence[i-1]:
            current_repeat += 1
            max_repeat = max(max_repeat, current_repeat)
        else:
            current_repeat = 1
    
    return max_repeat

def main():
    """主函数"""
    test_final_text()
    test_fountain_code_final()

if __name__ == "__main__":
    main()
