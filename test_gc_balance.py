#!/usr/bin/env python3
"""
测试GC平衡编码的DNA编码器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder

def test_gc_balance():
    """测试GC含量平衡"""
    print("=== 测试GC平衡编码 ===\n")
    
    # 创建编码器
    encoder = DNAEncoder(max_length=200, add_error_correction=True, use_fountain_code=False)
    
    # 测试文本
    test_text = "人类是有极限的，我不做人啦"
    test_data = test_text.encode('utf-8')
    
    print(f"原始文本: {test_text}")
    print(f"数据长度: {len(test_data)} 字节\n")
    
    # 编码
    dna_sequences = encoder.encode_data(test_data)
    
    print("DNA序列:")
    total_length = 0
    total_gc = 0
    
    for i, seq in enumerate(dna_sequences):
        print(f"序列 {i+1}: {seq}")
        
        # 计算GC含量
        gc_count = seq.count('G') + seq.count('C')
        gc_percentage = (gc_count / len(seq)) * 100
        total_length += len(seq)
        total_gc += gc_count
        
        print(f"  长度: {len(seq)} 碱基")
        print(f"  GC含量: {gc_percentage:.1f}%")
        
        # 检查连续重复
        max_repeat = check_consecutive_repeats(seq)
        print(f"  最长连续重复: {max_repeat}")
        print()
    
    # 总体统计
    overall_gc = (total_gc / total_length) * 100
    print(f"总体GC含量: {overall_gc:.1f}%")
    
    # 解码验证
    try:
        decoded_data = encoder.decode_data(dna_sequences)
        decoded_text = decoded_data.decode('utf-8')
        print(f"\n解码结果: {decoded_text}")
        print(f"编码解码成功: {decoded_text == test_text}")
    except Exception as e:
        print(f"解码失败: {e}")

def test_fountain_code():
    """测试喷泉码编码"""
    print("\n=== 测试喷泉码编码 ===\n")
    
    # 创建喷泉码编码器
    encoder = DNAEncoder(max_length=200, add_error_correction=True, 
                        use_fountain_code=True, fountain_redundancy=1.5)
    
    # 测试文本
    test_text = "人类是有极限的，我不做人啦"
    test_data = test_text.encode('utf-8')
    
    print(f"原始文本: {test_text}")
    print(f"数据长度: {len(test_data)} 字节\n")
    
    # 编码
    dna_sequences = encoder.encode_data(test_data)
    
    print("喷泉码DNA序列:")
    total_length = 0
    total_gc = 0
    
    for i, seq in enumerate(dna_sequences):
        print(f"序列 {i+1}: {seq}")
        
        # 计算GC含量（排除标识符）
        if seq.startswith("AAAA") and seq.endswith("TTTT"):
            content_seq = seq[4:-4]
        else:
            content_seq = seq
            
        gc_count = content_seq.count('G') + content_seq.count('C')
        gc_percentage = (gc_count / len(content_seq)) * 100 if content_seq else 0
        total_length += len(content_seq)
        total_gc += gc_count
        
        print(f"  内容长度: {len(content_seq)} 碱基")
        print(f"  GC含量: {gc_percentage:.1f}%")
        
        # 检查连续重复
        max_repeat = check_consecutive_repeats(content_seq)
        print(f"  最长连续重复: {max_repeat}")
        print()
    
    # 总体统计
    overall_gc = (total_gc / total_length) * 100 if total_length > 0 else 0
    print(f"总体GC含量: {overall_gc:.1f}%")
    
    # 解码验证
    try:
        decoded_data = encoder.decode_data(dna_sequences)
        decoded_text = decoded_data.decode('utf-8')
        print(f"\n解码结果: {decoded_text}")
        print(f"编码解码成功: {decoded_text == test_text}")
    except Exception as e:
        print(f"解码失败: {e}")

def check_consecutive_repeats(sequence):
    """检查最长连续重复碱基数量"""
    if not sequence:
        return 0
    
    max_repeat = 1
    current_repeat = 1
    
    for i in range(1, len(sequence)):
        if sequence[i] == sequence[i-1]:
            current_repeat += 1
            max_repeat = max(max_repeat, current_repeat)
        else:
            current_repeat = 1
    
    return max_repeat

def main():
    """主函数"""
    test_gc_balance()
    test_fountain_code()

if __name__ == "__main__":
    main()
