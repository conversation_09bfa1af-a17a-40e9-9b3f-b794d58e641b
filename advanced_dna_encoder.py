#!/usr/bin/env python3
"""
高级DNA数据存储编码器
包含多种编码策略、错误校正和序列优化
"""

import random
import hashlib
import math
from typing import List, Tuple, Optional, Dict
from collections import Counter

class AdvancedDNAEncoder:
    """高级DNA数据存储编码器"""
    
    # 多种编码方案
    ENCODING_SCHEMES = {
        'basic': {
            '00': 'A', '01': 'T', '10': 'G', '11': 'C'
        },
        'balanced': {
            '00': 'A', '01': 'C', '10': 'G', '11': 'T'
        },
        'quaternary': {
            '00': 'A', '01': 'T', '10': 'C', '11': 'G'
        }
    }
    
    def __init__(self, 
                 encoding_scheme: str = 'basic',
                 max_length: int = 200,
                 target_gc_content: float = 0.5,
                 avoid_homopolymers: bool = True,
                 max_homopolymer_length: int = 3):
        """
        初始化高级DNA编码器
        
        Args:
            encoding_scheme: 编码方案 ('basic', 'balanced', 'quaternary')
            max_length: 最大序列长度
            target_gc_content: 目标GC含量
            avoid_homopolymers: 是否避免同聚物
            max_homopolymer_length: 最大同聚物长度
        """
        self.encoding_scheme = encoding_scheme
        self.binary_to_dna = self.ENCODING_SCHEMES[encoding_scheme]
        self.dna_to_binary = {v: k for k, v in self.binary_to_dna.items()}
        self.max_length = max_length
        self.target_gc_content = target_gc_content
        self.avoid_homopolymers = avoid_homopolymers
        self.max_homopolymer_length = max_homopolymer_length
    
    def encode_with_optimization(self, data: bytes) -> List[str]:
        """
        使用优化策略编码数据
        
        Args:
            data: 要编码的数据
            
        Returns:
            优化后的DNA序列列表
        """
        # 1. 基本编码
        binary_str = self._prepare_binary_data(data)
        
        # 2. 应用优化策略
        optimized_binary = self._optimize_binary_sequence(binary_str)
        
        # 3. 转换为DNA
        dna_sequence = self._binary_to_dna(optimized_binary)
        
        # 4. 后处理优化
        optimized_dna = self._optimize_dna_sequence(dna_sequence)
        
        # 5. 分割序列
        return self._split_and_index_sequences(optimized_dna)
    
    def _prepare_binary_data(self, data: bytes) -> str:
        """准备二进制数据，添加元数据"""
        # 原始数据
        binary_str = ''.join(format(byte, '08b') for byte in data)
        
        # 添加长度信息
        length_info = format(len(data), '032b')
        
        # 添加校验和
        checksum = hashlib.sha256(data).digest()[:8]  # 使用前8字节
        checksum_binary = ''.join(format(byte, '08b') for byte in checksum)
        
        # 添加编码方案标识
        scheme_id = format(list(self.ENCODING_SCHEMES.keys()).index(self.encoding_scheme), '08b')
        
        return scheme_id + length_info + checksum_binary + binary_str
    
    def _optimize_binary_sequence(self, binary_str: str) -> str:
        """优化二进制序列以改善DNA特性"""
        if not self.avoid_homopolymers:
            return binary_str
        
        # 检测并修复可能导致长同聚物的模式
        optimized = ""
        i = 0
        while i < len(binary_str):
            if i + 1 < len(binary_str):
                current_pair = binary_str[i:i+2]
                next_pair = binary_str[i+2:i+4] if i+2 < len(binary_str) else ""
                
                # 检查是否会产生长同聚物
                current_base = self.binary_to_dna[current_pair]
                next_base = self.binary_to_dna.get(next_pair, "")
                
                if current_base == next_base and len(optimized) > 0:
                    last_base = self.binary_to_dna.get(optimized[-2:], "")
                    if last_base == current_base:
                        # 尝试替换以避免三连续相同碱基
                        alternative = self._find_alternative_encoding(current_pair)
                        optimized += alternative
                    else:
                        optimized += current_pair
                else:
                    optimized += current_pair
                i += 2
            else:
                optimized += binary_str[i]
                i += 1
        
        return optimized
    
    def _find_alternative_encoding(self, binary_pair: str) -> str:
        """寻找替代编码以避免同聚物"""
        # 简单策略：翻转一位
        alternatives = []
        for i in range(2):
            alt = list(binary_pair)
            alt[i] = '1' if alt[i] == '0' else '0'
            alternatives.append(''.join(alt))
        
        # 选择最佳替代方案（这里简化为随机选择）
        return random.choice(alternatives)
    
    def _optimize_dna_sequence(self, dna_sequence: str) -> str:
        """优化DNA序列"""
        # 检查GC含量
        gc_content = self._calculate_gc_content(dna_sequence)
        
        if abs(gc_content - self.target_gc_content) > 0.1:
            # 如果GC含量偏差太大，尝试调整
            return self._adjust_gc_content(dna_sequence)
        
        return dna_sequence
    
    def _adjust_gc_content(self, dna_sequence: str) -> str:
        """调整GC含量"""
        # 简化实现：在序列中插入平衡序列
        current_gc = self._calculate_gc_content(dna_sequence)
        
        if current_gc < self.target_gc_content:
            # 需要增加GC
            balancing_seq = "GC" * 10
        else:
            # 需要减少GC
            balancing_seq = "AT" * 10
        
        # 在序列中间插入平衡序列
        mid_point = len(dna_sequence) // 2
        return dna_sequence[:mid_point] + balancing_seq + dna_sequence[mid_point:]
    
    def _split_and_index_sequences(self, dna_sequence: str) -> List[str]:
        """分割序列并添加索引"""
        sequences = []
        sequence_count = (len(dna_sequence) + self.max_length - 1) // self.max_length
        
        for i in range(0, len(dna_sequence), self.max_length):
            chunk = dna_sequence[i:i+self.max_length]
            
            # 添加序列索引（简化版）
            index = format(i // self.max_length, '016b')  # 16位索引
            index_dna = self._binary_to_dna(index)
            
            # 添加同步标记
            sync_marker = "ATCGATCG"
            
            indexed_sequence = sync_marker + index_dna + chunk
            sequences.append(indexed_sequence)
        
        return sequences
    
    def decode_optimized_data(self, dna_sequences: List[str]) -> bytes:
        """解码优化后的数据"""
        # 1. 移除索引和同步标记
        cleaned_sequences = []
        for seq in dna_sequences:
            # 移除同步标记（前8个碱基）
            seq_without_sync = seq[8:]
            # 移除索引（接下来8个碱基，对应16位二进制）
            seq_without_index = seq_without_sync[8:]
            cleaned_sequences.append(seq_without_index)
        
        # 2. 重组完整序列
        full_dna = ''.join(cleaned_sequences)
        
        # 3. 转换为二进制
        binary_str = self._dna_to_binary(full_dna)
        
        # 4. 提取元数据
        scheme_id_binary = binary_str[:8]
        length_binary = binary_str[8:40]
        checksum_binary = binary_str[40:104]  # 8字节 = 64位
        data_binary = binary_str[104:]
        
        # 5. 验证编码方案
        scheme_id = int(scheme_id_binary, 2)
        expected_scheme = list(self.ENCODING_SCHEMES.keys())[scheme_id]
        if expected_scheme != self.encoding_scheme:
            print(f"警告：编码方案不匹配。期望：{expected_scheme}，当前：{self.encoding_scheme}")
        
        # 6. 获取原始数据长度
        original_length = int(length_binary, 2)
        
        # 7. 转换为字节数据
        while len(data_binary) % 8 != 0:
            data_binary = data_binary[:-1]
        
        data = bytes(int(data_binary[i:i+8], 2) for i in range(0, len(data_binary), 8))
        data = data[:original_length]
        
        # 8. 验证校验和
        expected_checksum = hashlib.sha256(data).digest()[:8]
        actual_checksum = bytes(int(checksum_binary[i:i+8], 2) 
                              for i in range(0, len(checksum_binary), 8))
        
        if expected_checksum != actual_checksum:
            raise ValueError("数据校验失败")
        
        return data
    
    def _binary_to_dna(self, binary_str: str) -> str:
        """二进制转DNA"""
        dna = ""
        for i in range(0, len(binary_str), 2):
            if i + 1 < len(binary_str):
                pair = binary_str[i:i+2]
                dna += self.binary_to_dna[pair]
        return dna
    
    def _dna_to_binary(self, dna_sequence: str) -> str:
        """DNA转二进制"""
        binary = ""
        for base in dna_sequence.upper():
            if base in self.dna_to_binary:
                binary += self.dna_to_binary[base]
        return binary
    
    def _calculate_gc_content(self, dna_sequence: str) -> float:
        """计算GC含量"""
        gc_count = dna_sequence.count('G') + dna_sequence.count('C')
        return gc_count / len(dna_sequence) if len(dna_sequence) > 0 else 0
    
    def analyze_sequences(self, dna_sequences: List[str]) -> Dict:
        """详细分析DNA序列"""
        analysis = {}
        
        # 基本统计
        total_length = sum(len(seq) for seq in dna_sequences)
        analysis['sequence_count'] = len(dna_sequences)
        analysis['total_length'] = total_length
        analysis['average_length'] = total_length / len(dna_sequences) if dna_sequences else 0
        
        # GC含量分析
        gc_contents = [self._calculate_gc_content(seq) for seq in dna_sequences]
        analysis['gc_content'] = {
            'average': sum(gc_contents) / len(gc_contents) if gc_contents else 0,
            'min': min(gc_contents) if gc_contents else 0,
            'max': max(gc_contents) if gc_contents else 0,
            'target': self.target_gc_content
        }
        
        # 同聚物分析
        max_homopolymer = 0
        for seq in dna_sequences:
            current_max = self._find_max_homopolymer(seq)
            max_homopolymer = max(max_homopolymer, current_max)
        
        analysis['homopolymer'] = {
            'max_length': max_homopolymer,
            'target_max': self.max_homopolymer_length,
            'compliant': max_homopolymer <= self.max_homopolymer_length
        }
        
        return analysis
    
    def _find_max_homopolymer(self, sequence: str) -> int:
        """找到序列中最长的同聚物"""
        if not sequence:
            return 0
        
        max_length = 1
        current_length = 1
        
        for i in range(1, len(sequence)):
            if sequence[i] == sequence[i-1]:
                current_length += 1
                max_length = max(max_length, current_length)
            else:
                current_length = 1
        
        return max_length


def demo_advanced_encoding():
    """演示高级DNA编码"""
    print("=== 高级DNA数据存储编码器演示 ===\n")
    
    # 测试不同编码方案
    schemes = ['basic', 'balanced', 'quaternary']
    test_data = b"Advanced DNA Storage Test! 高级DNA存储测试数据。"
    
    for scheme in schemes:
        print(f"--- 使用 {scheme} 编码方案 ---")
        encoder = AdvancedDNAEncoder(
            encoding_scheme=scheme,
            max_length=80,
            target_gc_content=0.5,
            avoid_homopolymers=True,
            max_homopolymer_length=3
        )
        
        # 编码
        dna_sequences = encoder.encode_with_optimization(test_data)
        
        # 分析
        analysis = encoder.analyze_sequences(dna_sequences)
        print(f"序列数量: {analysis['sequence_count']}")
        print(f"平均GC含量: {analysis['gc_content']['average']:.3f}")
        print(f"最大同聚物长度: {analysis['homopolymer']['max_length']}")
        print(f"同聚物合规: {analysis['homopolymer']['compliant']}")
        
        # 解码验证
        try:
            decoded = encoder.decode_optimized_data(dna_sequences)
            print(f"解码成功: {decoded == test_data}")
        except Exception as e:
            print(f"解码失败: {e}")
        
        print()


if __name__ == "__main__":
    demo_advanced_encoding()
