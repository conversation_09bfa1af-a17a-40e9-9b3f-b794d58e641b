#!/usr/bin/env python3
"""
使用DNA编码方案转换指定文本
"""

# 使用您方案中的编码映射
BINARY_TO_DNA = {
    '00': 'A',
    '01': 'T', 
    '10': 'G',
    '11': 'C'
}

def encode_text_to_dna(text):
    """将文本编码为DNA序列"""
    print(f"原始文本: {text}")
    
    # 1. 转换为UTF-8字节
    text_bytes = text.encode('utf-8')
    print(f"UTF-8字节: {text_bytes}")
    print(f"字节长度: {len(text_bytes)} 字节")
    
    # 2. 转换为二进制字符串
    binary_str = ''.join(format(byte, '08b') for byte in text_bytes)
    print(f"二进制表示: {binary_str}")
    print(f"二进制长度: {len(binary_str)} 位")
    
    # 3. 如果需要，添加填充使长度为2的倍数
    if len(binary_str) % 2 != 0:
        binary_str += '0'
        print(f"填充后二进制: {binary_str}")
    
    # 4. 转换为DNA序列
    dna_sequence = ''
    for i in range(0, len(binary_str), 2):
        two_bits = binary_str[i:i+2]
        dna_sequence += BINARY_TO_DNA[two_bits]
    
    print(f"\nDNA序列: {dna_sequence}")
    print(f"DNA长度: {len(dna_sequence)} 个碱基")
    
    # 5. 分析DNA序列
    base_count = {'A': 0, 'T': 0, 'G': 0, 'C': 0}
    for base in dna_sequence:
        base_count[base] += 1
    
    print(f"\n碱基统计:")
    for base, count in base_count.items():
        percentage = (count / len(dna_sequence)) * 100
        print(f"  {base}: {count} ({percentage:.1f}%)")
    
    gc_content = (base_count['G'] + base_count['C']) / len(dna_sequence) * 100
    print(f"  GC含量: {gc_content:.1f}%")
    
    # 6. 格式化输出（每行80个碱基）
    print(f"\n格式化DNA序列（每行80个碱基）:")
    for i in range(0, len(dna_sequence), 80):
        line_num = i // 80 + 1
        line = dna_sequence[i:i+80]
        print(f"{line_num:2d}: {line}")
    
    return dna_sequence

def main():
    """主函数"""
    text = "人类是有极限的，我不做人啦"
    
    print("=== 使用您的DNA编码方案转换文本 ===")
    print("编码映射: 00→A, 01→T, 10→G, 11→C")
    print()
    
    dna_result = encode_text_to_dna(text)
    
    print(f"\n=== 最终结果 ===")
    print(f"完整DNA序列:")
    print(dna_result)

if __name__ == "__main__":
    main()
