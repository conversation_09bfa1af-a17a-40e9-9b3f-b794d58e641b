#!/usr/bin/env python3
"""
DNA数据存储系统演示
展示完整的编码、存储和解码流程
"""

import os
import tempfile
from pathlib import Path
from dna_encoder import DNAEncoder
from advanced_dna_encoder import AdvancedDNAEncoder
from dna_file_processor import DNAFileProcessor

def create_test_files():
    """创建测试文件"""
    test_dir = Path("test_files")
    test_dir.mkdir(exist_ok=True)
    
    # 文本文件
    with open(test_dir / "sample.txt", 'w', encoding='utf-8') as f:
        f.write("""DNA数据存储技术演示

这是一个测试文件，用于演示DNA数据存储技术。
DNA存储具有以下优势：
1. 超高密度存储
2. 长期稳定性
3. 可复制性
4. 环保特性

This is a test file for DNA data storage demonstration.
DNA storage has the following advantages:
1. Ultra-high density storage
2. Long-term stability  
3. Replicability
4. Environmental friendliness
""")
    
    # 二进制文件（模拟图片数据）
    binary_data = bytes(range(256)) * 10  # 2560字节的测试数据
    with open(test_dir / "binary_sample.bin", 'wb') as f:
        f.write(binary_data)
    
    print(f"测试文件已创建在 {test_dir} 目录中")
    return test_dir

def demo_basic_encoding():
    """演示基础编码功能"""
    print("=" * 60)
    print("基础DNA编码演示")
    print("=" * 60)
    
    encoder = DNAEncoder(max_length=100, add_error_correction=True)
    
    # 测试数据
    test_data = b"Hello DNA World! 你好DNA世界！"
    print(f"原始数据: {test_data}")
    print(f"数据长度: {len(test_data)} 字节")
    
    # 编码
    dna_sequences = encoder.encode_data(test_data)
    print(f"\n编码后的DNA序列 ({len(dna_sequences)} 个):")
    for i, seq in enumerate(dna_sequences):
        print(f"  序列 {i+1}: {seq}")
    
    # 分析
    analysis = encoder.analyze_sequences(dna_sequences)
    print(f"\n序列分析:")
    for key, value in analysis.items():
        print(f"  {key}: {value}")
    
    # 解码验证
    try:
        decoded_data = encoder.decode_data(dna_sequences)
        success = decoded_data == test_data
        print(f"\n解码结果: {'成功' if success else '失败'}")
        if success:
            print(f"解码数据: {decoded_data}")
    except Exception as e:
        print(f"\n解码失败: {e}")

def demo_advanced_encoding():
    """演示高级编码功能"""
    print("\n" + "=" * 60)
    print("高级DNA编码演示")
    print("=" * 60)
    
    # 测试不同编码方案
    schemes = ['basic', 'balanced', 'quaternary']
    test_data = b"Advanced DNA Storage! 高级DNA存储技术测试数据，包含中英文混合内容。"
    
    print(f"测试数据: {test_data}")
    print(f"数据长度: {len(test_data)} 字节\n")
    
    for scheme in schemes:
        print(f"--- {scheme.upper()} 编码方案 ---")
        
        encoder = AdvancedDNAEncoder(
            encoding_scheme=scheme,
            max_length=80,
            target_gc_content=0.5,
            avoid_homopolymers=True,
            max_homopolymer_length=3
        )
        
        # 编码
        dna_sequences = encoder.encode_with_optimization(test_data)
        
        # 分析
        analysis = encoder.analyze_sequences(dna_sequences)
        print(f"序列数量: {analysis['sequence_count']}")
        print(f"总长度: {analysis['total_length']}")
        print(f"平均GC含量: {analysis['gc_content']['average']:.3f} (目标: {analysis['gc_content']['target']})")
        print(f"最大同聚物: {analysis['homopolymer']['max_length']} (限制: {analysis['homopolymer']['target_max']})")
        print(f"同聚物合规: {'是' if analysis['homopolymer']['compliant'] else '否'}")
        
        # 解码验证
        try:
            decoded = encoder.decode_optimized_data(dna_sequences)
            success = decoded == test_data
            print(f"解码验证: {'成功' if success else '失败'}")
        except Exception as e:
            print(f"解码失败: {e}")
        
        print()

def demo_file_processing():
    """演示文件处理功能"""
    print("=" * 60)
    print("文件处理演示")
    print("=" * 60)
    
    # 创建测试文件
    test_dir = create_test_files()
    
    # 创建文件处理器
    processor = DNAFileProcessor(
        use_advanced=True,
        encoding_scheme='balanced',
        max_length=150,
        target_gc_content=0.5,
        avoid_homopolymers=True
    )
    
    # 编码文本文件
    print("\n--- 编码文本文件 ---")
    text_file = test_dir / "sample.txt"
    result = processor.encode_file(str(text_file), "demo_output")
    
    print(f"编码结果:")
    print(f"  DNA文件: {result['dna_file']}")
    print(f"  序列数量: {result['sequences']}")
    print(f"  压缩比: {result['compression_ratio']:.3f}")
    
    # 解码验证
    print("\n--- 解码验证 ---")
    decoded_file = processor.decode_file(result['dna_file'])
    
    # 比较原文件和解码文件
    with open(text_file, 'rb') as f:
        original_data = f.read()
    with open(decoded_file, 'rb') as f:
        decoded_data = f.read()
    
    print(f"文件完整性: {'验证成功' if original_data == decoded_data else '验证失败'}")
    
    # 编码二进制文件
    print("\n--- 编码二进制文件 ---")
    binary_file = test_dir / "binary_sample.bin"
    result2 = processor.encode_file(str(binary_file), "demo_output")
    
    print(f"二进制文件编码:")
    print(f"  序列数量: {result2['sequences']}")
    print(f"  压缩比: {result2['compression_ratio']:.3f}")
    
    # 解码二进制文件
    decoded_binary = processor.decode_file(result2['dna_file'])
    
    with open(binary_file, 'rb') as f:
        original_binary = f.read()
    with open(decoded_binary, 'rb') as f:
        decoded_binary_data = f.read()
    
    print(f"二进制文件完整性: {'验证成功' if original_binary == decoded_binary_data else '验证失败'}")

def demo_storage_efficiency():
    """演示存储效率分析"""
    print("\n" + "=" * 60)
    print("存储效率分析")
    print("=" * 60)
    
    # 不同大小的测试数据
    test_sizes = [100, 1000, 10000]  # 字节
    
    for size in test_sizes:
        print(f"\n--- {size} 字节数据测试 ---")
        
        # 生成测试数据
        test_data = os.urandom(size)
        
        # 基础编码
        basic_encoder = DNAEncoder(max_length=200)
        basic_sequences = basic_encoder.encode_data(test_data)
        basic_dna_length = sum(len(seq) for seq in basic_sequences)
        
        # 高级编码
        advanced_encoder = AdvancedDNAEncoder(max_length=200)
        advanced_sequences = advanced_encoder.encode_with_optimization(test_data)
        advanced_dna_length = sum(len(seq) for seq in advanced_sequences)
        
        print(f"原始数据: {size} 字节")
        print(f"基础编码: {len(basic_sequences)} 序列, {basic_dna_length} 碱基")
        print(f"高级编码: {len(advanced_sequences)} 序列, {advanced_dna_length} 碱基")
        print(f"理论最小: {size * 4} 碱基 (每字节4个碱基)")
        print(f"基础效率: {(size * 4) / basic_dna_length * 100:.1f}%")
        print(f"高级效率: {(size * 4) / advanced_dna_length * 100:.1f}%")

def main():
    """主演示函数"""
    print("DNA数据存储系统完整演示")
    print("=" * 60)
    
    try:
        # 基础编码演示
        demo_basic_encoding()
        
        # 高级编码演示
        demo_advanced_encoding()
        
        # 文件处理演示
        demo_file_processing()
        
        # 存储效率分析
        demo_storage_efficiency()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\n生成的文件:")
        print("- demo_output/: 编码后的DNA文件和元数据")
        print("- test_files/: 测试用的原始文件")
        print("\n使用方法:")
        print("1. 基础编码: python dna_encoder.py")
        print("2. 高级编码: python advanced_dna_encoder.py") 
        print("3. 文件处理: python dna_file_processor.py encode <文件路径>")
        print("4. 批量处理: python dna_file_processor.py batch <目录路径>")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
