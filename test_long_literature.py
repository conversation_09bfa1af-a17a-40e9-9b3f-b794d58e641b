#!/usr/bin/env python3
"""
测试DNA编码系统处理长文学文本的能力
使用公共领域的经典文本片段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder

def test_classical_chinese_text():
    """测试经典中文文本"""
    # 使用《论语》片段（公共领域）
    classical_text = """
    子曰："学而时习之，不亦说乎？有朋自远方来，不亦乐乎？人不知而不愠，不亦君子乎？"
    有子曰："其为人也孝弟，而好犯上者，鲜矣；不好犯上，而好作乱者，未之有也。君子务本，本立而道生。孝弟也者，其为仁之本与！"
    子曰："巧言令色，鲜矣仁！"
    曾子曰："吾日三省吾身——为人谋而不忠乎？与朋友交而不信乎？传不习乎？"
    子曰："君子食无求饱，居无求安，敏于事而慎于言，就有道而正焉，可谓好学也已。"
    子贡曰："贫而无谄，富而无骄，何如？"子曰："可也。未若贫而乐，富而好礼者也。"
    子贡曰："《诗》云：'如切如磋，如琢如磨'，其斯之谓与？"子曰："赐也，始可与言《诗》已矣，告诸往而知来者。"
    """
    
    print("=== 测试经典中文文本（论语片段）===")
    print(f"原始文本长度: {len(classical_text)} 字符")
    print(f"UTF-8字节长度: {len(classical_text.encode('utf-8'))} 字节")
    
    # 传统编码测试
    encoder_traditional = DNAEncoder(max_length=200, add_error_correction=False, use_fountain_code=False)
    dna_sequences = encoder_traditional.encode_data(classical_text.encode('utf-8'))
    
    print(f"\n传统编码结果:")
    print(f"DNA序列数量: {len(dna_sequences)}")
    
    total_length = 0
    total_gc = 0
    max_repeat = 0
    
    for i, seq in enumerate(dna_sequences):
        # 计算统计信息
        gc_count = seq.count('G') + seq.count('C')
        gc_percent = (gc_count / len(seq)) * 100
        
        # 检查连续重复
        current_max_repeat = get_max_repeat(seq)
        max_repeat = max(max_repeat, current_max_repeat)
        
        total_length += len(seq)
        total_gc += gc_count
        
        print(f"  序列 {i+1}: {len(seq)} 碱基, GC: {gc_percent:.1f}%, 最长重复: {current_max_repeat}")
    
    overall_gc = (total_gc / total_length) * 100
    print(f"\n总体统计:")
    print(f"  总长度: {total_length} 碱基")
    print(f"  总体GC含量: {overall_gc:.1f}%")
    print(f"  最长连续重复: {max_repeat}")
    print(f"  GC含量理想范围: {'是' if 40 <= overall_gc <= 60 else '否'}")
    
    # 验证解码
    try:
        decoded_data = encoder_traditional.decode_data(dna_sequences)
        decoded_text = decoded_data.decode('utf-8')
        success = decoded_text == classical_text
        print(f"  解码成功: {success}")
        if not success:
            print(f"  原文长度: {len(classical_text)}")
            print(f"  解码长度: {len(decoded_text)}")
    except Exception as e:
        print(f"  解码失败: {e}")

def test_fountain_code_long_text():
    """测试喷泉码处理长文本"""
    # 生成一个较长的测试文本
    base_text = "这是一个测试DNA数据存储系统处理长文本能力的实验。"
    long_text = base_text * 20  # 重复20次创建长文本
    
    print(f"\n=== 测试喷泉码长文本处理 ===")
    print(f"测试文本长度: {len(long_text)} 字符")
    print(f"UTF-8字节长度: {len(long_text.encode('utf-8'))} 字节")
    
    # 喷泉码编码测试
    encoder_fountain = DNAEncoder(max_length=200, add_error_correction=False, 
                                 use_fountain_code=True, fountain_redundancy=1.5)
    
    dna_sequences = encoder_fountain.encode_data(long_text.encode('utf-8'))
    
    print(f"\n喷泉码编码结果:")
    print(f"DNA序列数量: {len(dna_sequences)}")
    
    total_length = 0
    total_gc = 0
    max_repeat = 0
    
    for i, seq in enumerate(dna_sequences):
        # 提取内容（去掉标识符）
        if seq.startswith("AAAA") and seq.endswith("TTTT"):
            content = seq[4:-4]
        else:
            content = seq
        
        # 计算统计信息
        gc_count = content.count('G') + content.count('C')
        gc_percent = (gc_count / len(content)) * 100 if len(content) > 0 else 0
        
        # 检查连续重复
        current_max_repeat = get_max_repeat(content)
        max_repeat = max(max_repeat, current_max_repeat)
        
        total_length += len(content)
        total_gc += gc_count
        
        print(f"  序列 {i+1}: {len(content)} 碱基, GC: {gc_percent:.1f}%, 最长重复: {current_max_repeat}")
    
    overall_gc = (total_gc / total_length) * 100 if total_length > 0 else 0
    redundancy = len(dna_sequences) * 200 / len(long_text.encode('utf-8')) if len(long_text.encode('utf-8')) > 0 else 0
    
    print(f"\n总体统计:")
    print(f"  总长度: {total_length} 碱基")
    print(f"  总体GC含量: {overall_gc:.1f}%")
    print(f"  最长连续重复: {max_repeat}")
    print(f"  冗余度: {redundancy:.1f}")
    print(f"  GC含量理想范围: {'是' if 40 <= overall_gc <= 60 else '否'}")
    print(f"  重复控制良好: {'是' if max_repeat < 4 else '否'}")
    
    # 验证解码
    try:
        decoded_data = encoder_fountain.decode_data(dna_sequences)
        decoded_text = decoded_data.decode('utf-8')
        success = decoded_text == long_text
        print(f"  解码成功: {success}")
        if not success:
            print(f"  原文长度: {len(long_text)}")
            print(f"  解码长度: {len(decoded_text)}")
    except Exception as e:
        print(f"  解码失败: {e}")

def get_max_repeat(sequence):
    """获取序列中最长的连续重复长度"""
    if len(sequence) < 2:
        return 0
    
    max_repeat = 1
    current_repeat = 1
    
    for i in range(1, len(sequence)):
        if sequence[i] == sequence[i-1]:
            current_repeat += 1
            max_repeat = max(max_repeat, current_repeat)
        else:
            current_repeat = 1
    
    return max_repeat

def test_mixed_content():
    """测试混合内容（中英文、数字、标点）"""
    mixed_text = """
    DNA数据存储技术(DNA Data Storage)是一种新兴的数据存储方法，它利用DNA分子的四种碱基（A、T、G、C）来编码数字信息。
    
    这种技术具有以下优势：
    1. 存储密度极高：1克DNA可以存储约215PB的数据
    2. 保存时间长：在适当条件下可保存数千年
    3. 能耗低：不需要持续供电维护
    4. 抗干扰能力强：对电磁干扰免疫
    
    Technical specifications:
    - Encoding: Binary to quaternary (ATGC)
    - Error correction: Reed-Solomon codes
    - GC content: 40-60% optimal
    - Maximum consecutive repeats: <4 bases
    
    应用前景包括：长期档案存储、基因组数据库、历史文献保护等领域。
    """
    
    print(f"\n=== 测试混合内容文本 ===")
    print(f"文本长度: {len(mixed_text)} 字符")
    print(f"UTF-8字节长度: {len(mixed_text.encode('utf-8'))} 字节")
    
    # 使用传统编码
    encoder = DNAEncoder(max_length=200, add_error_correction=False, use_fountain_code=False)
    dna_sequences = encoder.encode_data(mixed_text.encode('utf-8'))
    
    print(f"\n编码结果:")
    print(f"DNA序列数量: {len(dna_sequences)}")
    
    # 验证解码
    try:
        decoded_data = encoder.decode_data(dna_sequences)
        decoded_text = decoded_data.decode('utf-8')
        success = decoded_text == mixed_text
        print(f"解码成功: {success}")
        
        if success:
            print("✅ 混合内容编码解码完全成功！")
        else:
            print("❌ 解码结果与原文不匹配")
    except Exception as e:
        print(f"解码失败: {e}")

def main():
    """主函数"""
    print("🧬 DNA编码系统长文本处理能力测试")
    print("=" * 60)
    
    test_classical_chinese_text()
    test_fountain_code_long_text()
    test_mixed_content()
    
    print("\n" + "=" * 60)
    print("测试完成！")

if __name__ == "__main__":
    main()
