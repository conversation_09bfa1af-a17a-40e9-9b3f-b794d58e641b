#!/usr/bin/env python3
"""
将指定文本转换为DNA序列
"""

import hashlib

class SimpleDNAEncoder:
    """简化的DNA编码器"""
    
    # 基本编码映射：2位二进制 -> 1个DNA碱基
    BINARY_TO_DNA = {
        '00': 'A',
        '01': 'T', 
        '10': 'G',
        '11': 'C'
    }
    
    # 解码映射：DNA碱基 -> 2位二进制
    DNA_TO_BINARY = {v: k for k, v in BINARY_TO_DNA.items()}
    
    def encode_text(self, text: str) -> str:
        """将文本编码为DNA序列"""
        # 1. 转换为UTF-8字节
        text_bytes = text.encode('utf-8')
        
        # 2. 转换为二进制字符串
        binary_str = ''.join(format(byte, '08b') for byte in text_bytes)
        
        # 3. 如果需要，添加填充使长度为2的倍数
        if len(binary_str) % 2 != 0:
            binary_str += '0'
        
        # 4. 转换为DNA序列
        dna_sequence = ''
        for i in range(0, len(binary_str), 2):
            two_bits = binary_str[i:i+2]
            dna_sequence += self.BINARY_TO_DNA[two_bits]
        
        return dna_sequence
    
    def decode_dna(self, dna_sequence: str) -> str:
        """将DNA序列解码为文本"""
        # 1. 转换为二进制
        binary_str = ''
        for base in dna_sequence.upper():
            if base in self.DNA_TO_BINARY:
                binary_str += self.DNA_TO_BINARY[base]
        
        # 2. 转换为字节
        text_bytes = bytes(int(binary_str[i:i+8], 2) for i in range(0, len(binary_str), 8))
        
        # 3. 解码为文本
        try:
            return text_bytes.decode('utf-8')
        except UnicodeDecodeError:
            # 如果有填充位，尝试去掉最后一个字节
            return text_bytes[:-1].decode('utf-8')

def main():
    """主函数"""
    encoder = SimpleDNAEncoder()
    
    # 要编码的文本
    text = "人类是有极限的，我不做人啦"
    
    print("=== 文本到DNA序列转换 ===")
    print(f"原始文本: {text}")
    
    # 显示UTF-8编码信息
    text_bytes = text.encode('utf-8')
    print(f"UTF-8字节: {text_bytes}")
    print(f"字节长度: {len(text_bytes)} 字节")
    
    # 显示二进制表示
    binary_str = ''.join(format(byte, '08b') for byte in text_bytes)
    print(f"二进制表示: {binary_str}")
    print(f"二进制长度: {len(binary_str)} 位")
    
    # 编码为DNA
    dna_sequence = encoder.encode_text(text)
    print(f"\nDNA序列: {dna_sequence}")
    print(f"DNA长度: {len(dna_sequence)} 个碱基")
    
    # 分析DNA序列
    base_count = {'A': 0, 'T': 0, 'G': 0, 'C': 0}
    for base in dna_sequence:
        base_count[base] += 1
    
    print(f"\n碱基统计:")
    for base, count in base_count.items():
        percentage = (count / len(dna_sequence)) * 100
        print(f"  {base}: {count} ({percentage:.1f}%)")
    
    gc_content = (base_count['G'] + base_count['C']) / len(dna_sequence) * 100
    print(f"  GC含量: {gc_content:.1f}%")
    
    # 验证解码
    decoded_text = encoder.decode_dna(dna_sequence)
    print(f"\n解码验证:")
    print(f"解码文本: {decoded_text}")
    print(f"编码解码成功: {decoded_text == text}")
    
    # 格式化输出（每行80个碱基）
    print(f"\n格式化DNA序列（每行80个碱基）:")
    for i in range(0, len(dna_sequence), 80):
        line_num = i // 80 + 1
        line = dna_sequence[i:i+80]
        print(f"{line_num:2d}: {line}")

if __name__ == "__main__":
    main()
