#!/usr/bin/env python3
"""
DNA数据存储编码器
将二进制数据转换为DNA序列(AGCT)，并支持解码回原始数据
"""

import random
import hashlib
from typing import List, Tuple, Optional

class DNAEncoder:
    """DNA数据存储编码器类"""
    
    # 基本编码映射：2位二进制 -> 1个DNA碱基
    BINARY_TO_DNA = {
        '00': 'A',
        '01': 'T', 
        '10': 'G',
        '11': 'C'
    }
    
    # 解码映射：DNA碱基 -> 2位二进制
    DNA_TO_BINARY = {v: k for k, v in BINARY_TO_DNA.items()}
    
    def __init__(self, max_length: int = 200, add_error_correction: bool = True):
        """
        初始化DNA编码器
        
        Args:
            max_length: 单个DNA序列的最大长度（模拟实际DNA合成限制）
            add_error_correction: 是否添加错误校正码
        """
        self.max_length = max_length
        self.add_error_correction = add_error_correction
    
    def encode_data(self, data: bytes) -> List[str]:
        """
        将字节数据编码为DNA序列列表
        
        Args:
            data: 要编码的字节数据
            
        Returns:
            DNA序列列表
        """
        # 1. 转换为二进制字符串
        binary_str = ''.join(format(byte, '08b') for byte in data)
        
        # 2. 如果需要，添加填充使长度为2的倍数
        if len(binary_str) % 2 != 0:
            binary_str += '0'
        
        # 3. 添加数据长度信息（用于解码时去除填充）
        original_length = len(data)
        length_binary = format(original_length, '032b')  # 32位长度信息
        binary_str = length_binary + binary_str
        
        # 4. 如果启用错误校正，添加校验和
        if self.add_error_correction:
            checksum = hashlib.md5(data).digest()
            checksum_binary = ''.join(format(byte, '08b') for byte in checksum)
            binary_str = checksum_binary + binary_str
        
        # 5. 转换为DNA序列
        dna_sequence = self._binary_to_dna(binary_str)
        
        # 6. 分割为多个序列（模拟DNA合成长度限制）
        return self._split_sequence(dna_sequence)
    
    def decode_data(self, dna_sequences: List[str]) -> bytes:
        """
        将DNA序列列表解码为原始字节数据
        
        Args:
            dna_sequences: DNA序列列表
            
        Returns:
            解码后的字节数据
        """
        # 1. 合并所有DNA序列
        full_dna = ''.join(dna_sequences)
        
        # 2. 转换为二进制
        binary_str = self._dna_to_binary(full_dna)
        
        # 3. 提取校验和（如果有）
        if self.add_error_correction:
            checksum_binary = binary_str[:128]  # MD5是128位
            binary_str = binary_str[128:]
        
        # 4. 提取长度信息
        length_binary = binary_str[:32]
        original_length = int(length_binary, 2)
        binary_str = binary_str[32:]
        
        # 5. 转换为字节数据
        # 确保二进制字符串长度是8的倍数
        while len(binary_str) % 8 != 0:
            binary_str = binary_str[:-1]  # 移除填充
        
        data = bytes(int(binary_str[i:i+8], 2) for i in range(0, len(binary_str), 8))
        
        # 6. 截取到原始长度
        data = data[:original_length]
        
        # 7. 验证校验和（如果有）
        if self.add_error_correction:
            expected_checksum = hashlib.md5(data).digest()
            actual_checksum_binary = checksum_binary
            actual_checksum = bytes(int(actual_checksum_binary[i:i+8], 2) 
                                  for i in range(0, len(actual_checksum_binary), 8))
            
            if expected_checksum != actual_checksum:
                raise ValueError("数据校验失败，可能存在错误")
        
        return data
    
    def _binary_to_dna(self, binary_str: str) -> str:
        """将二进制字符串转换为DNA序列"""
        dna_sequence = ''
        for i in range(0, len(binary_str), 2):
            two_bits = binary_str[i:i+2]
            if len(two_bits) == 2:
                dna_sequence += self.BINARY_TO_DNA[two_bits]
        return dna_sequence
    
    def _dna_to_binary(self, dna_sequence: str) -> str:
        """将DNA序列转换为二进制字符串"""
        binary_str = ''
        for base in dna_sequence.upper():
            if base in self.DNA_TO_BINARY:
                binary_str += self.DNA_TO_BINARY[base]
        return binary_str
    
    def _split_sequence(self, dna_sequence: str) -> List[str]:
        """将长DNA序列分割为多个短序列"""
        sequences = []
        for i in range(0, len(dna_sequence), self.max_length):
            sequences.append(dna_sequence[i:i+self.max_length])
        return sequences
    
    def add_primers(self, dna_sequences: List[str], 
                   forward_primer: str = "ATCGATCG", 
                   reverse_primer: str = "CGATCGAT") -> List[str]:
        """
        为DNA序列添加引物（用于PCR扩增）
        
        Args:
            dna_sequences: DNA序列列表
            forward_primer: 正向引物
            reverse_primer: 反向引物
            
        Returns:
            添加引物后的DNA序列列表
        """
        return [forward_primer + seq + reverse_primer for seq in dna_sequences]
    
    def calculate_gc_content(self, dna_sequence: str) -> float:
        """计算GC含量（重要的DNA稳定性指标）"""
        gc_count = dna_sequence.count('G') + dna_sequence.count('C')
        return gc_count / len(dna_sequence) if len(dna_sequence) > 0 else 0
    
    def analyze_sequences(self, dna_sequences: List[str]) -> dict:
        """分析DNA序列的特性"""
        total_length = sum(len(seq) for seq in dna_sequences)
        gc_contents = [self.calculate_gc_content(seq) for seq in dna_sequences]
        
        return {
            'sequence_count': len(dna_sequences),
            'total_length': total_length,
            'average_length': total_length / len(dna_sequences) if dna_sequences else 0,
            'average_gc_content': sum(gc_contents) / len(gc_contents) if gc_contents else 0,
            'gc_content_range': (min(gc_contents), max(gc_contents)) if gc_contents else (0, 0)
        }


def demo_usage():
    """演示DNA编码器的使用"""
    print("=== DNA数据存储编码器演示 ===\n")
    
    # 创建编码器
    encoder = DNAEncoder(max_length=100, add_error_correction=True)
    
    # 测试数据
    test_data = b"Hello, DNA Storage World! 这是一个DNA数据存储测试。"
    print(f"原始数据: {test_data}")
    print(f"数据长度: {len(test_data)} 字节\n")
    
    # 编码
    dna_sequences = encoder.encode_data(test_data)
    print("编码后的DNA序列:")
    for i, seq in enumerate(dna_sequences):
        print(f"序列 {i+1}: {seq}")
    print()
    
    # 分析序列特性
    analysis = encoder.analyze_sequences(dna_sequences)
    print("序列分析:")
    for key, value in analysis.items():
        print(f"  {key}: {value}")
    print()
    
    # 解码
    try:
        decoded_data = encoder.decode_data(dna_sequences)
        print(f"解码后的数据: {decoded_data}")
        print(f"解码成功: {decoded_data == test_data}")
    except ValueError as e:
        print(f"解码失败: {e}")


if __name__ == "__main__":
    demo_usage()
