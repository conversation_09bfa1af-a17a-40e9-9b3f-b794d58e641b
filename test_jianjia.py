#!/usr/bin/env python3
"""
测试诗经《蒹葭》DNA编码
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder
import time

def get_max_repeat(sequence):
    """获取序列中最长的连续重复长度"""
    if len(sequence) < 2:
        return 0
    
    max_repeat = 1
    current_repeat = 1
    
    for i in range(1, len(sequence)):
        if sequence[i] == sequence[i-1]:
            current_repeat += 1
            max_repeat = max(max_repeat, current_repeat)
        else:
            current_repeat = 1
    
    return max_repeat

def test_jianjia():
    """测试诗经《蒹葭》"""
    # 诗经《蒹葭》全文（公共领域）
    jianjia_text = """蒹葭

蒹葭苍苍，白露为霜。
所谓伊人，在水一方。
溯洄从之，道阻且长。
溯游从之，宛在水中央。

蒹葭萋萋，白露未晞。
所谓伊人，在水之湄。
溯洄从之，道阻且跻。
溯游从之，宛在水中坻。

蒹葭采采，白露未已。
所谓伊人，在水之涘。
溯洄从之，道阻且右。
溯游从之，宛在水中沚。"""

    print("🧬 诗经《蒹葭》DNA编码测试")
    print("=" * 60)
    
    print("📜 原文：")
    print(jianjia_text)
    print()
    
    print(f"📊 文本信息:")
    print(f"  字符数: {len(jianjia_text):,}")
    print(f"  UTF-8字节数: {len(jianjia_text.encode('utf-8')):,}")
    print(f"  行数: {jianjia_text.count(chr(10)) + 1}")
    
    # 传统编码测试
    print(f"\n=== 传统编码测试 ===")
    encoder_traditional = DNAEncoder(max_length=200, add_error_correction=True, use_fountain_code=False)
    
    start_time = time.time()
    dna_sequences = encoder_traditional.encode_data(jianjia_text.encode('utf-8'))
    encoding_time = time.time() - start_time
    
    print(f"编码耗时: {encoding_time:.4f} 秒")
    print(f"DNA序列数量: {len(dna_sequences)}")
    
    # 分析每个序列
    total_length = 0
    total_gc = 0
    max_repeat_overall = 0
    
    for i, seq in enumerate(dna_sequences):
        # 计算统计信息
        gc_count = seq.count('G') + seq.count('C')
        gc_percent = (gc_count / len(seq)) * 100
        
        # 检查连续重复
        max_repeat = get_max_repeat(seq)
        max_repeat_overall = max(max_repeat_overall, max_repeat)
        
        total_length += len(seq)
        total_gc += gc_count
        
        print(f"  序列 {i+1}: {len(seq)} 碱基")
        print(f"    DNA: {seq}")
        print(f"    GC含量: {gc_percent:.1f}%")
        print(f"    最长重复: {max_repeat}")
        print()
    
    # 总体统计
    overall_gc = (total_gc / total_length) * 100 if total_length > 0 else 0
    
    print(f"=== 总体统计 ===")
    print(f"总长度: {total_length:,} 碱基")
    print(f"总体GC含量: {overall_gc:.1f}%")
    print(f"最长连续重复: {max_repeat_overall}")
    print(f"GC含量理想范围(40-60%): {'✅ 是' if 40 <= overall_gc <= 60 else '❌ 否'}")
    print(f"重复控制良好(<4): {'✅ 是' if max_repeat_overall < 4 else '❌ 否'}")
    
    # 计算存储效率
    original_bits = len(jianjia_text.encode('utf-8')) * 8
    dna_bits = total_length * 2  # 每个碱基2位
    compression_ratio = dna_bits / original_bits
    
    print(f"压缩比: {compression_ratio:.2f}")
    print(f"存储效率: {1/compression_ratio:.2f}")
    
    # 验证解码
    print(f"\n=== 解码验证 ===")
    try:
        start_time = time.time()
        decoded_data = encoder_traditional.decode_data(dna_sequences)
        decoding_time = time.time() - start_time
        
        decoded_text = decoded_data.decode('utf-8')
        success = decoded_text == jianjia_text
        
        print(f"解码耗时: {decoding_time:.4f} 秒")
        print(f"解码成功: {'✅ 是' if success else '❌ 否'}")
        
        if success:
            print(f"✅ 完美！《蒹葭》原文完全恢复")
            print(f"\n📜 解码后的文本：")
            print(decoded_text)
        else:
            print(f"❌ 解码失败")
            print(f"原文长度: {len(jianjia_text)}")
            print(f"解码长度: {len(decoded_text)}")
    
    except Exception as e:
        print(f"❌ 解码失败: {e}")
    
    return {
        'total_length': total_length,
        'gc_percent': overall_gc,
        'max_repeat': max_repeat_overall,
        'sequence_count': len(dna_sequences),
        'success': success if 'success' in locals() else False
    }

def test_jianjia_fountain_code():
    """测试《蒹葭》喷泉码编码"""
    jianjia_text = """蒹葭

蒹葭苍苍，白露为霜。
所谓伊人，在水一方。
溯洄从之，道阻且长。
溯游从之，宛在水中央。

蒹葭萋萋，白露未晞。
所谓伊人，在水之湄。
溯洄从之，道阻且跻。
溯游从之，宛在水中坻。

蒹葭采采，白露未已。
所谓伊人，在水之涘。
溯洄从之，道阻且右。
溯游从之，宛在水中沚。"""

    print(f"\n" + "=" * 60)
    print(f"=== 喷泉码编码测试 ===")
    
    # 喷泉码编码
    encoder_fountain = DNAEncoder(max_length=200, add_error_correction=True, 
                                 use_fountain_code=True, fountain_redundancy=1.5)
    
    try:
        start_time = time.time()
        dna_sequences = encoder_fountain.encode_data(jianjia_text.encode('utf-8'))
        encoding_time = time.time() - start_time
        
        print(f"编码耗时: {encoding_time:.4f} 秒")
        print(f"DNA序列数量: {len(dna_sequences)}")
        
        # 分析结果
        total_length = 0
        total_gc = 0
        max_repeat_overall = 0
        
        for i, seq in enumerate(dna_sequences):
            # 提取内容（去掉标识符）
            if seq.startswith("AAAA") and seq.endswith("TTTT"):
                content = seq[4:-4]
            else:
                content = seq
            
            # 计算统计信息
            gc_count = content.count('G') + content.count('C')
            gc_percent = (gc_count / len(content)) * 100 if len(content) > 0 else 0
            
            # 检查连续重复
            max_repeat = get_max_repeat(content)
            max_repeat_overall = max(max_repeat_overall, max_repeat)
            
            total_length += len(content)
            total_gc += gc_count
            
            print(f"  序列 {i+1}: {len(content)} 碱基, GC: {gc_percent:.1f}%, 最长重复: {max_repeat}")
        
        # 总体统计
        overall_gc = (total_gc / total_length) * 100 if total_length > 0 else 0
        original_bytes = len(jianjia_text.encode('utf-8'))
        redundancy = (len(dna_sequences) * 200) / (original_bytes * 4) if original_bytes > 0 else 0
        
        print(f"\n喷泉码总体统计:")
        print(f"  总长度: {total_length:,} 碱基")
        print(f"  总体GC含量: {overall_gc:.1f}%")
        print(f"  最长连续重复: {max_repeat_overall}")
        print(f"  冗余度: {redundancy:.2f}")
        print(f"  GC含量理想范围: {'✅ 是' if 40 <= overall_gc <= 60 else '❌ 否'}")
        print(f"  重复控制良好: {'✅ 是' if max_repeat_overall < 4 else '❌ 否'}")
        
        # 验证解码
        print(f"\n=== 喷泉码解码验证 ===")
        try:
            start_time = time.time()
            decoded_data = encoder_fountain.decode_data(dna_sequences)
            decoding_time = time.time() - start_time
            
            decoded_text = decoded_data.decode('utf-8')
            success = decoded_text == jianjia_text
            
            print(f"解码耗时: {decoding_time:.4f} 秒")
            print(f"解码成功: {'✅ 是' if success else '❌ 否'}")
            
            if success:
                print(f"✅ 喷泉码编码解码成功！")
            else:
                print(f"❌ 喷泉码解码失败")
        
        except Exception as e:
            print(f"❌ 喷泉码解码失败: {e}")
    
    except Exception as e:
        print(f"❌ 喷泉码编码失败: {e}")

def main():
    """主函数"""
    # 测试传统编码
    stats = test_jianjia()
    
    # 测试喷泉码编码
    test_jianjia_fountain_code()
    
    print(f"\n" + "=" * 60)
    print(f"🎉 诗经《蒹葭》DNA编码测试完成！")
    
    if stats['success']:
        print(f"✅ 传统编码完美处理了 {stats['total_length']:,} 个碱基")
        print(f"✅ GC含量: {stats['gc_percent']:.1f}% (理想范围)")
        print(f"✅ 数据完整性: 100%")
        print(f"🧬 千年古诗现在可以永久保存在DNA分子中！")
    else:
        print(f"❌ 编码测试未完全成功")

if __name__ == "__main__":
    main()
