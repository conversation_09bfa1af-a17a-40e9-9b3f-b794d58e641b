#!/usr/bin/env python3
"""
调试喷泉码编码解码
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder

def debug_fountain_code():
    """调试喷泉码编码解码过程"""
    print("=== 调试喷泉码编码解码 ===\n")
    
    # 创建喷泉码编码器
    encoder = DNAEncoder(max_length=200, add_error_correction=False, 
                        use_fountain_code=True, fountain_redundancy=1.2)
    
    # 测试简单文本
    test_text = "Hello World"
    test_data = test_text.encode('utf-8')
    
    print(f"原始文本: {test_text}")
    print(f"数据长度: {len(test_data)} 字节")
    print(f"数据内容: {test_data.hex()}")
    
    # 1. 先测试喷泉码本身
    print(f"\n=== 测试喷泉码本身 ===")
    fountain_blocks = encoder.fountain_code.encode(test_data)
    print(f"喷泉码块数量: {len(fountain_blocks)}")
    
    for i, block in enumerate(fountain_blocks):
        print(f"块 {i+1}:")
        print(f"  ID: {block['id']}")
        print(f"  度数: {block['degree']}")
        print(f"  邻居: {block['neighbors']}")
        print(f"  数据: {block['data'].hex()}")
    
    # 2. 手动测试头信息编码
    print(f"\n=== 手动测试头信息编码 ===")
    for i, block in enumerate(fountain_blocks):
        neighbors_str = ','.join(map(str, sorted(block['neighbors']))) if block['neighbors'] else ''
        header_str = f"{len(test_data)}:{block['id']}:{block['degree']}:{neighbors_str}"
        header_bytes = header_str.encode('utf-8')

        print(f"块 {i+1} 头信息:")
        print(f"  头信息字符串: {repr(header_str)}")
        print(f"  头信息字节: {header_bytes.hex()}")
        print(f"  头信息长度: {len(header_bytes)}")
        print(f"  长度编码: {len(header_bytes).to_bytes(2, 'big').hex()}")

        # 组合数据
        combined_data = len(header_bytes).to_bytes(2, 'big') + header_bytes + block['data']
        print(f"  组合数据长度: {len(combined_data)}")
        print(f"  组合数据前20字节: {combined_data[:20].hex()}")

        # 测试解析
        parsed_header_length = int.from_bytes(combined_data[:2], 'big')
        parsed_header_bytes = combined_data[2:2+parsed_header_length]
        parsed_block_data = combined_data[2+parsed_header_length:]

        print(f"  解析的头信息长度: {parsed_header_length}")
        print(f"  解析的头信息: {repr(parsed_header_bytes.decode('utf-8'))}")
        print(f"  解析的块数据长度: {len(parsed_block_data)}")
        print(f"  解析的块数据前20字节: {parsed_block_data[:20].hex()}")

        # 测试DNA编码解码
        print(f"\n  测试DNA编码解码:")
        dna_seq = encoder._encode_gc_balanced(combined_data)
        print(f"    DNA序列长度: {len(dna_seq)}")
        print(f"    DNA序列前20: {dna_seq[:20]}")

        decoded_combined = encoder._decode_gc_balanced(dna_seq)
        print(f"    解码后长度: {len(decoded_combined)}")
        print(f"    解码后前20字节: {decoded_combined[:20].hex()}")
        print(f"    解码匹配: {decoded_combined == combined_data}")

        if decoded_combined != combined_data:
            print(f"    原始数据: {combined_data.hex()}")
            print(f"    解码数据: {decoded_combined.hex()}")

            # 检查解码后的头信息
            if len(decoded_combined) >= 2:
                decoded_header_length = int.from_bytes(decoded_combined[:2], 'big')
                print(f"    解码的头信息长度: {decoded_header_length}")
                if len(decoded_combined) >= 2 + decoded_header_length:
                    decoded_header_bytes = decoded_combined[2:2+decoded_header_length]
                    print(f"    解码的头信息: {repr(decoded_header_bytes.decode('utf-8', errors='replace'))}")

    # 3. 测试喷泉码解码
    print(f"\n=== 测试喷泉码解码 ===")
    try:
        decoded_data = encoder.fountain_code.decode(fountain_blocks, len(test_data))
        decoded_text = decoded_data.decode('utf-8')
        print(f"喷泉码解码成功: {decoded_text}")
        print(f"解码正确: {decoded_text == test_text}")
    except Exception as e:
        print(f"喷泉码解码失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. 测试完整的DNA编码
    print(f"\n=== 测试完整DNA编码 ===")
    try:
        dna_sequences = encoder.encode_data(test_data)
        print(f"DNA序列数量: {len(dna_sequences)}")
        
        for i, seq in enumerate(dna_sequences):
            print(f"\n序列 {i+1}:")
            print(f"  长度: {len(seq)}")
            print(f"  前20个碱基: {seq[:20]}...")
            print(f"  后20个碱基: ...{seq[-20:]}")
            
            # 检查标识符
            if seq.startswith("AAAA") and seq.endswith("TTTT"):
                content = seq[4:-4]
                print(f"  内容长度: {len(content)}")
                
                # 手动解码内容
                try:
                    decoded_content = encoder._decode_gc_balanced(content)
                    print(f"  解码内容长度: {len(decoded_content)} 字节")
                    
                    # 解析头信息
                    if len(decoded_content) >= 2:
                        header_length = int.from_bytes(decoded_content[:2], 'big')
                        print(f"  头信息长度: {header_length}")
                        
                        if len(decoded_content) >= 2 + header_length:
                            header_bytes = decoded_content[2:2+header_length]
                            block_data = decoded_content[2+header_length:]
                            
                            header_str = header_bytes.decode('utf-8')
                            print(f"  头信息: {repr(header_str)}")
                            print(f"  块数据长度: {len(block_data)}")
                            print(f"  块数据: {block_data.hex()}")

                            # 检查头信息格式
                            parts = header_str.split(':')
                            print(f"  头信息部分: {parts}")
                            if len(parts) >= 4:
                                print(f"    原始大小: {parts[0]}")
                                print(f"    块ID: {parts[1]}")
                                print(f"    度数: {parts[2]}")
                                print(f"    邻居: {parts[3]}")
                            else:
                                print(f"    头信息格式错误，部分数量: {len(parts)}")
                        else:
                            print(f"  数据太短，无法解析完整头信息")
                    else:
                        print(f"  数据太短，无法解析头信息长度")
                        
                except Exception as e:
                    print(f"  手动解码失败: {e}")
        
        # 5. 测试完整解码
        print(f"\n=== 测试完整解码 ===")
        try:
            decoded_data = encoder.decode_data(dna_sequences)
            decoded_text = decoded_data.decode('utf-8')
            print(f"完整解码成功: {decoded_text}")
            print(f"解码正确: {decoded_text == test_text}")
        except Exception as e:
            print(f"完整解码失败: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"DNA编码失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    debug_fountain_code()

if __name__ == "__main__":
    main()
