#!/usr/bin/env python3
"""
DNA数据存储编码器（增强版）
支持错误校验、GC平衡分析、非法碱基检测、更安全的解码
"""

import hashlib
from typing import List, Dict, Any

class DNAEncoder:
    """增强版DNA数据存储编码器"""
    
    BINARY_TO_DNA = {'00': 'A', '01': 'T', '10': 'G', '11': 'C'}
    DNA_TO_BINARY = {v: k for k, v in BINARY_TO_DNA.items()}
    
    def __init__(self, max_length: int = 200, add_error_correction: bool = True):
        self.max_length = max_length
        self.add_error_correction = add_error_correction
    
    def encode_data(self, data: bytes) -> List[str]:
        """编码字节数据为DNA序列"""
        # 二进制转换
        binary_str = ''.join(format(b, '08b') for b in data)
        
        # 确保长度为2的倍数
        if len(binary_str) % 2 != 0:
            binary_str += '0'  # 补零
        
        # 添加长度（32位）和可选校验和
        length_bin = format(len(data), '032b')
        binary_str = length_bin + binary_str
        
        if self.add_error_correction:
            checksum = hashlib.md5(data).digest()
            checksum_bin = ''.join(format(b, '08b') for b in checksum)
            binary_str = checksum_bin + binary_str
        
        # 转DNA
        dna_seq = self._binary_to_dna(binary_str)
        
        # 分割
        return self._split_sequence(dna_seq)
    
    def decode_data(self, dna_sequences: List[str]) -> bytes:
        """解码DNA序列为原始数据"""
        full_dna = ''.join(seq.strip() for seq in dna_sequences if seq.strip())
        
        # 转二进制，严格检查碱基
        binary_str = self._dna_to_binary_strict(full_dna)
        
        pos = 0
        
        # 提取校验和
        if self.add_error_correction:
            if len(binary_str) < 128:
                raise ValueError("数据过短，无法提取MD5校验和")
            checksum_bin = binary_str[pos:pos+128]
            pos += 128
        
        # 提取长度
        if len(binary_str) < pos + 32:
            raise ValueError("数据过短，无法提取长度信息")
        length_bin = binary_str[pos:pos+32]
        original_length = int(length_bin, 2)
        pos += 32
        
        # 提取数据
        data_bin = binary_str[pos:]
        
        # 移除末尾填充，确保8位对齐
        data_bin = data_bin[:len(data_bin) // 8 * 8]
        if len(data_bin) == 0:
            raise ValueError("无有效数据")
        
        data_bytes = bytes(
            int(data_bin[i:i+8], 2) for i in range(0, len(data_bin), 8)
        )
        
        # 截取原始长度
        data_bytes = data_bytes[:original_length]
        
        # 校验
        if self.add_error_correction:
            expected_checksum = hashlib.md5(data_bytes).digest()
            actual_checksum = bytes(
                int(checksum_bin[i:i+8], 2) for i in range(0, 128, 8)
            )
            if expected_checksum != actual_checksum:
                raise ValueError("校验失败：数据可能已损坏")
        
        return data_bytes
    
    def _binary_to_dna(self, binary_str: str) -> str:
        return ''.join(self.BINARY_TO_DNA[binary_str[i:i+2]] 
                      for i in range(0, len(binary_str), 2) if i+2 <= len(binary_str))
    
    def _dna_to_binary_strict(self, dna_sequence: str) -> str:
        """严格模式：非法碱基报错"""
        binary = ''
        for i, base in enumerate(dna_sequence.upper()):
            if base not in self.DNA_TO_BINARY:
                raise ValueError(f"非法碱基 '{base}' 在位置 {i}")
            binary += self.DNA_TO_BINARY[base]
        return binary
    
    def _split_sequence(self, dna_sequence: str) -> List[str]:
        return [dna_sequence[i:i+self.max_length] 
                for i in range(0, len(dna_sequence), self.max_length)]
    
    def add_primers(self, sequences: List[str], forward: str = "ATCGATCG", reverse: str = "CGATCGAT") -> List[str]:
        return [forward + seq + reverse for seq in sequences]
    
    def calculate_gc_content(self, seq: str) -> float:
        seq = seq.upper()
        gc = seq.count('G') + seq.count('C')
        return gc / len(seq) if seq else 0.0
    
    def analyze_sequences(self, sequences: List[str]) -> Dict[str, Any]:
        if not sequences:
            return {}
        lengths = [len(seq) for seq in sequences]
        gc_contents = [self.calculate_gc_content(seq) for seq in sequences]
        return {
            'sequence_count': len(sequences),
            'total_length': sum(lengths),
            'average_length': sum(lengths) / len(lengths),
            'min_length': min(lengths),
            'max_length': max(lengths),
            'average_gc_content': sum(gc_contents) / len(gc_contents),
            'gc_content_range': (min(gc_contents), max(gc_contents)),
        }