#!/usr/bin/env python3
"""
测试特定字节的编码解码
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dna_gui import DNAEncoder

def test_specific_bytes():
    """测试特定字节"""
    print("=== 测试特定字节编码解码 ===\n")
    
    encoder = DNAEncoder()
    
    # 测试问题数据
    problem_data = bytes.fromhex("000831313a303a313a3048656c6c6f20576f726c64000000000000000000000000000000000000000000")
    
    print(f"原始数据: {problem_data.hex()}")
    print(f"数据长度: {len(problem_data)}")
    
    # 编码
    dna_seq = encoder._encode_gc_balanced(problem_data)
    print(f"DNA序列: {dna_seq}")
    print(f"DNA长度: {len(dna_seq)}")
    
    # 解码
    decoded_data = encoder._decode_gc_balanced(dna_seq)
    print(f"解码数据: {decoded_data.hex()}")
    print(f"解码长度: {len(decoded_data)}")
    
    print(f"编码解码匹配: {decoded_data == problem_data}")
    
    if decoded_data != problem_data:
        print("\n=== 详细比较 ===")
        min_len = min(len(problem_data), len(decoded_data))
        for i in range(min_len):
            if problem_data[i] != decoded_data[i]:
                print(f"位置 {i}: 原始=0x{problem_data[i]:02X}, 解码=0x{decoded_data[i]:02X}")
        
        if len(problem_data) != len(decoded_data):
            print(f"长度不匹配: 原始={len(problem_data)}, 解码={len(decoded_data)}")
    
    # 逐字节测试
    print(f"\n=== 逐字节编码测试 ===")
    for i, byte_val in enumerate(problem_data):
        high_nibble = (byte_val >> 4) & 0x0F
        low_nibble = byte_val & 0x0F
        
        high_pair = encoder.nibble_to_pair[high_nibble]
        low_pair = encoder.nibble_to_pair[low_nibble]
        
        expected_dna = high_pair + low_pair
        
        # 单字节编码
        single_byte_dna = encoder._encode_gc_balanced(bytes([byte_val]))
        
        print(f"字节 {i:2d}: 0x{byte_val:02X} -> {high_nibble:X}({high_pair}) + {low_nibble:X}({low_pair}) = {expected_dna}")
        print(f"         实际DNA: {single_byte_dna}")
        print(f"         匹配: {single_byte_dna == expected_dna}")
        
        if single_byte_dna != expected_dna:
            print(f"         *** 不匹配! ***")

def test_problematic_sequence():
    """测试有问题的序列"""
    print(f"\n=== 测试有问题的序列 ===")
    
    encoder = DNAEncoder()
    
    # 测试连续的零字节
    zero_bytes = b'\x00\x00\x00\x00'
    print(f"零字节: {zero_bytes.hex()}")
    
    dna_seq = encoder._encode_gc_balanced(zero_bytes)
    print(f"DNA序列: {dna_seq}")
    
    decoded = encoder._decode_gc_balanced(dna_seq)
    print(f"解码: {decoded.hex()}")
    print(f"匹配: {decoded == zero_bytes}")

def main():
    """主函数"""
    test_specific_bytes()
    test_problematic_sequence()

if __name__ == "__main__":
    main()
